<template>
  <view class="edit-inventory-container">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 库存数量显示 -->
      <view class="form-item">
        <view class="form-label">
          <text>库存数量</text>
        </view>
        <view class="form-value">
          <text class="form-text">{{ formData.number }}</text>
        </view>
      </view>

      <!-- 操作类型 -->
      <view class="form-item">
        <view class="form-label required">
          <text>操作类型</text>
        </view>
        <view class="form-value" @tap="showOperationTypePicker">
          <text class="form-text">{{ operationTypeDisplay }}</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 数量输入 -->
      <view class="form-item">
        <view class="form-label required">
          <text>数量</text>
        </view>
        <view class="form-value">
          <input
            type="number"
            v-model="formData.num"
            placeholder="请输入"
            placeholder-class="form-placeholder"
            class="form-input"
          />
        </view>
      </view>

      <!-- 采购订单选择器（仅入库操作显示） -->
      <view class="form-item" v-if="formData.type == '2'">
        <view class="form-label">
          <text>采购订单</text>
          <text class="optional-text">（可选）</text>
        </view>
        <view class="form-value" @tap="showOrderSelector">
          <text v-if="formData.orderDisplay" class="form-text">{{ formData.orderDisplay }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 备注输入 -->
      <view class="form-item">
        <view class="form-label">
          <text>备注</text>
        </view>
        <view class="form-value">
          <input
            type="text"
            v-model="formData.remark"
            placeholder="请输入"
            placeholder-class="form-placeholder"
            class="form-input"
          />
        </view>
      </view>
    </view>

    <!-- 维保订单选择器 -->
    <SingleLevelPicker
      v-model="showOrderPopup"
      :options="orderList"
      title="选择采购订单"
      :value="formData.orderId"
      @select="handleOrderSelect"
    />

    <!-- 操作类型选择器 -->
    <SingleLevelPicker
      v-model="showOperationType"
      :options="stock_type"
      title="选择操作类型"
      text-field="label"
      value-field="value"
      :value="formData.type"
      @select="handleOperationTypeSelect"
    />

    <!-- 提交按钮 -->
    <view class="submit-btn-container">
      <button class="submit-btn" @tap="handleSubmit">提交</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getUnprocessedDevices, updateInventoryOperation } from '@/api/inventory'
import SingleLevelPicker from '@/components/SingleLevelPicker/SingleLevelPicker.vue'

const { proxy } = getCurrentInstance()

const { stock_type } = proxy.useTypeList('stock_type')

// 维保订单数据
const orderList = ref([])
// stock_type: [
//   {
//     label: '库存调整',
//     value: '0'
//   },
//   {
//     label: '出库',
//     value: '1'
//   },
//   {
//     label: '入库',
//     value: '2'
//   }
// ],

// 表单数据
const formData = ref({
  deviceId: '',
  number: '', // 库存数量
  type: '0', // 默认库存调整操作
  num: '', // 数量
  orderId: '', // 维保订单ID
  orderDisplay: '', // 维保订单显示文本
  remark: '', // 备注
})

// 控制弹窗
const showOrderPopup = ref(false)
const showOperationType = ref(false)

// 操作类型显示文本
const operationTypeDisplay = computed(() => {
  const type = stock_type.find(t => t.value === formData.value.type)
  return type ? type.label : ''
})

// 显示操作类型选择器
const showOperationTypePicker = () => {
  showOperationType.value = true
}

// 处理操作类型选择
const handleOperationTypeSelect = (_, value) => {
  formData.value.type = value
  // 重置相关字段（入库操作是 '2'）
  if (value !== '2') {
    formData.value.orderId = ''
    formData.value.orderDisplay = ''
  }
}

// 显示订单选择器
const showOrderSelector = () => {
  // 每次打开弹窗时都重新加载数据
  loadOrderList()
  showOrderPopup.value = true
}

// 加载维保订单列表
const loadOrderList = async () => {
  try {
    proxy.$modal.loading('加载中...')
    const res = await getUnprocessedDevices({ deviceId: formData.value.deviceId })
    orderList.value = res.data.map(item => ({
      label: item.orderCode,
      value: item.maintainOrderDetailId,
    }))
  } catch (error) {
    proxy.$modal.msgError('获取采购订单失败')
    orderList.value = []
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 处理订单选择
const handleOrderSelect = order => {
  formData.value.orderId = order.value
  formData.value.orderDisplay = order.label
}

// 表单验证
const validateForm = () => {
  if (!formData.value.type) {
    proxy.$modal.msg('请选择操作类型')
    return false
  }

  if (!formData.value.num) {
    proxy.$modal.msg('请输入数量')
    return false
  }

  const quantity = Number(formData.value.num)
  if (quantity <= 0) {
    proxy.$modal.msg('数量必须大于0')
    return false
  }

  // 出库操作验证库存是否足够（出库操作是 '1'）
  if (formData.value.type === '1') {
    if (quantity > formData.value.number) {
      proxy.$modal.msg('出库数量不能大于当前库存')
      return false
    }
  }

  // 入库操作时采购订单为可选项，无需验证

  return true
}

// 处理提交
const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    proxy.$modal.loading('处理中...')

    const submitData = {
      deviceId: formData.value.deviceId,
      type: formData.value.type,
      num: Number(formData.value.num),
      number: formData.value.number,
      remark: formData.value.remark,
    }

    // 入库操作需要添加订单ID（入库操作是 '2'）
    if (formData.value.type === '2') {
      submitData.orderId = formData.value.orderId
    }

    // 调用库存操作API
    await updateInventoryOperation(submitData)

    proxy.$modal.msgSuccess('操作成功')

    // 触发刷新事件

    // 返回上一页
    setTimeout(() => {
      uni.$emit('refreshInventoryList')
      proxy.$tab.navigateBack()
    }, 1000)
  } catch (error) {
    console.error('操作失败:', error)
    proxy.$modal.msg('操作失败')
  } finally {
    proxy.$modal.closeLoading()
  }
}

onLoad(() => {
  const currentInventory = uni.getStorageSync('currentInventory')
  if (currentInventory) {
    // 设置设备ID
    formData.value.deviceId = currentInventory.deviceId
    formData.value.number = currentInventory.stockNum
  }
  uni.removeStorageSync('currentInventory')
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.edit-inventory-container {
  display: flex;
  flex-direction: column;
  height: $uni-height-area;
  background-color: #f5f5f5;
  padding: 30rpx;
}

.form-container {
  background-color: #fff;
  padding: 0 30rpx;
  flex: 1;
  overflow-y: auto;
  border-radius: 20rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.required::before {
  content: '*';
  color: #ff0000;
  margin-right: 4rpx;
}

.optional-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

.form-value {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.form-text {
  font-size: 32rpx;
  color: #333;
  margin-right: 10rpx;
}

.form-placeholder {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}

.form-input {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  width: 100%;
}

.submit-btn-container {
  margin-top: 20rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background-color: #2979ff;
  color: #fff;
  font-size: 36rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
