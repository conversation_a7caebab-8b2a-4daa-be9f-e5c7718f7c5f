<template>
  <view class="maintenance-records-container">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 操作类型 -->
      <view class="form-item">
        <view class="form-label required">
          <text>操作类型</text>
        </view>
        <view class="form-value" @tap="showOperationTypePicker">
          <text v-if="getOperationTypeLabel(formData.type)" class="form-text">{{
            getOperationTypeLabel(formData.type)
          }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 开始时间 -->
      <view class="form-item">
        <view class="form-label required">
          <text>开始时间</text>
        </view>
        <view class="form-value datetime-picker-wrapper">
          <uni-datetime-picker
            type="datetime"
            v-model="formData.startTime"
            @change="handleStartTimeChange"
            :border="false"
            :clear-icon="false"
          >
            <view class="datetime-display">
              <text v-if="formData.startTime" class="form-text">{{ formData.startTime }}</text>
              <text v-else class="form-placeholder">请选择</text>
              <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
            </view>
          </uni-datetime-picker>
        </view>
      </view>

      <!-- 维保期限 -->
      <view class="form-item">
        <view class="form-label required">
          <text>维保期限</text>
        </view>
        <view class="form-value" @tap="showMaintenPeriodPicker">
          <text v-if="formData.maintenPeriod" class="form-text">{{
            timePicker.formatTime(formData.maintenPeriod)
          }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 具体位置 -->
      <view class="form-item">
        <view class="form-label required">
          <text>具体位置</text>
        </view>
        <view class="form-value" @tap="showLocationPicker">
          <text v-if="pointInfo.name" class="form-text">{{ pointInfo.name }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 设备类型 -->
      <view class="form-item">
        <view class="form-label required">
          <text>设备类型</text>
        </view>
        <view class="form-value" @tap="showDeviceTypePicker">
          <text v-if="deviceTypeInfo.name" class="form-text">{{ deviceTypeInfo.name }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 设备名称 -->
      <view class="form-item">
        <view class="form-label required">
          <text>设备名称</text>
        </view>
        <view class="form-value" @tap="showDeviceNamePicker">
          <text v-if="deviceNameInfo.name" class="form-text">{{ deviceNameInfo.name }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 备注 -->
      <view class="form-item">
        <view class="form-label">
          <text>备注</text>
        </view>
        <view class="form-value">
          <input
            type="text"
            v-model="formData.remark"
            placeholder="请输入"
            placeholder-class="form-placeholder"
            class="form-input"
          />
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn-container">
      <button class="submit-btn" @tap="handleSubmit">提交</button>
    </view>
  </view>

  <!-- 维保期限选择器 -->
  <CustomTimePicker
    v-model="showMaintenPeriod"
    v-model:value="formData.maintenPeriod"
    title="维保期限"
    ref="timePicker"
  />

  <!-- 位置选择器 -->
  <LocationPickerPopup
    v-model="showLocationPopup"
    v-model:selected-value="formData.pointId"
    label-field="structureName"
    value-field="structureId"
    @confirm="handleLocationConfirm"
    @reset="handleLocationReset"
    @point-matched="handlePointMatched"
  />

  <!-- 设备类型选择器 -->
  <DeviceTypePicker
    v-model="showDeviceType"
    v-model:selectedDeviceType="formData.deviceType"
    @select="handleDeviceTypeSelect"
  />

  <!-- 设备名称选择器 -->
  <CustomActionSheet
    v-model="showDeviceName"
    :options="deviceNameList"
    title="选择设备名称"
    @select="handleDeviceNameSelect"
  />
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomActionSheet from '@/components/CustomActionSheet/CustomActionSheet.vue'
import CustomTimePicker from '@/components/CustomTimePicker/CustomTimePicker.vue'
import LocationPickerPopup from '@/components/LocationPickerPopup/LocationPickerPopup.vue'
import DeviceTypePicker from '@/components/DeviceTypePicker/DeviceTypePicker.vue'
import { getDeviceNameList, addMainten } from '@/api/device'

const { proxy } = getCurrentInstance()
const { device_oper_type } = proxy.useDict(['device_oper_type'])
const showOperationType = ref(false)
const showMaintenPeriod = ref(false)
const showLocationPopup = ref(false)
const showDeviceType = ref(false)
const showDeviceName = ref(false)
const timePicker = ref(null)

// 设备名称数据
const deviceNameList = ref([])

// 表单数据
const formData = reactive({
  type: '', // 操作类型
  startTime: '', // 开始时间
  maintenPeriod: 0, // 维保期限（秒）
  deviceType: '', // 设备类型
  deviceName: '', // 设备名称
  id: '', // 设备 id
  pointId: '', // 点位 id
  remark: '', // 备注
})

// 点位信息（用于显示）
const pointInfo = ref({
  id: '',
  name: '',
})

// 设备类型信息（用于显示）
const deviceTypeInfo = ref({
  id: '',
  name: '',
})

// 设备名称信息（用于显示）
const deviceNameInfo = ref({
  name: '',
})

// 显示操作类型选择器
function showOperationTypePicker() {
  showOperationType.value = true
}

// 显示维保期限选择器
function showMaintenPeriodPicker() {
  showMaintenPeriod.value = true
}

// 显示位置选择器
function showLocationPicker() {
  showLocationPopup.value = true
}

// 处理位置选择确认
function handleLocationConfirm(location) {
  // 使用structureId字段（与API数据结构一致）
  formData.pointId = location.structureId || location.id
  pointInfo.value.id = location.structureId || location.id
  pointInfo.value.name = location.structureName || location.name
}

// 处理位置选择重置
function handleLocationReset() {
  formData.pointId = ''
  pointInfo.value.id = ''
  pointInfo.value.name = ''
}

// 处理点位匹配事件（组件内部自动匹配时触发）
function handlePointMatched(pointData) {
  // 更新点位显示信息
  pointInfo.value.id = pointData.structureId || pointData.id
  pointInfo.value.name = pointData.structureName || pointData.name
}

// 根据操作类型值获取对应的标签文本
function getOperationTypeLabel(value) {
  if (!value) return ''
  const item = device_oper_type.value.find(item => item.value === value)
  return item ? item.label : ''
}

// 处理开始时间变化
function handleStartTimeChange(value) {
  formData.startTime = value
}

// 页面加载
onLoad(options => {
  formData.id = options.deviceId || ''
  formData.pointId = options.pointId || ''
  formData.deviceType = options.categoryId || ''
  formData.deviceName = options.categoryName || ''

  // 如果有设备类型，设置显示信息
  if (formData.deviceType) {
    deviceTypeInfo.value.id = formData.deviceType
    deviceTypeInfo.value.name = options.categoryName || ''
  }

  // 如果有设备名称，设置显示信息
  if (formData.deviceName) {
    deviceNameInfo.value.name = formData.deviceName
  }

  // 点位信息将由LocationPickerPopup组件内部自动匹配和回显
})

// 显示设备类型选择器
function showDeviceTypePicker() {
  showDeviceType.value = true
}

// 显示设备名称选择器
function showDeviceNamePicker() {
  if (!formData.deviceType) {
    proxy.$modal.msg('请先选择设备类型')
    return
  }
  showDeviceName.value = true
}

// 处理设备类型选择
function handleDeviceTypeSelect(deviceType) {
  deviceTypeInfo.value = {
    id: deviceType.id,
    name: deviceType.fullName,
  }
  formData.deviceType = deviceType.id

  // 清空设备名称
  deviceNameInfo.value.name = ''
  formData.deviceName = ''

  // 获取设备名称列表
  getDeviceNameOptions()
}

// 获取设备名称列表
async function getDeviceNameOptions() {
  try {
    proxy.$modal.loading('加载中...')
    const res = await getDeviceNameList({ categoryId: formData.deviceType })
    deviceNameList.value = res.data
  } catch (error) {
    proxy.$modal.msgError('获取设备名称失败')
    deviceNameList.value = []
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 处理设备名称选择
function handleDeviceNameSelect(_, value) {
  deviceNameInfo.value.name = value
  formData.deviceName = value
}

// 提交表单
function handleSubmit() {
  // 表单验证
  if (!formData.type) {
    return proxy.$modal.msg('请选择操作类型')
  }
  if (!formData.startTime) {
    return proxy.$modal.msg('请选择开始时间')
  }
  if (!formData.maintenPeriod) {
    return proxy.$modal.msg('请选择维保期限')
  }
  if (!formData.pointId) {
    return proxy.$modal.msg('请选择具体位置')
  }
  if (!formData.deviceType) {
    return proxy.$modal.msg('请选择设备类型')
  }
  if (!formData.deviceName) {
    return proxy.$modal.msg('请选择设备名称')
  }

  // TODO: 提交表单数据
  uni.showLoading({ title: '提交中...' })
  try {
    addMainten(formData).then(res => {
      uni.hideLoading()
      proxy.$modal.msgSuccess('提交成功')

      // 提交成功后返回上一页
      setTimeout(() => {
        proxy.$tab.navigateBack()
      }, 1500)
    })
  } catch (error) {
    uni.hideLoading()
    proxy.$modal.msgError('提交失败')
  }
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.maintenance-records-container {
  display: flex;
  flex-direction: column;
  height: $uni-height-area;
  background-color: #f5f5f5;
  padding: 30rpx;
}

.form-container {
  background-color: #fff;
  padding: 0 30rpx;
  flex: 1;
  overflow-y: auto;
  border-radius: 20rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.required::before {
  content: '*';
  color: #ff0000;
  margin-right: 4rpx;
}

.form-value {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.datetime-picker-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;

  :deep(.uni-date) {
    width: 100%;

    .uni-date-editor {
      width: 100%;
      border: none !important;
      padding: 0;
    }

    .uni-date-x {
      border: none !important;
    }
  }
}

.datetime-display {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.form-text {
  font-size: 32rpx;
  color: #333;
  margin-right: 10rpx;
}

.form-placeholder {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}

.form-input {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  width: 100%;
}

.submit-btn-container {
  margin-top: 20rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background-color: #2979ff;
  color: #fff;
  font-size: 36rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
