<template>
  <view class="basic-info">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" />
    </view>

    <!-- 基本信息内容 -->
    <view v-else class="info-content">
      <!-- 设备名称 -->
      <view class="info-item">
        <text class="label">设备名称</text>
        <text class="value">{{ detail.deviceName }}</text>
      </view>

      <!-- 设备图片 -->
      <view class="info-item">
        <text class="label">设备图片</text>
        <view class="image-container">
          <image v-if="detail.deviceImg" :src="detail.deviceImg" class="device-image" mode="aspectFit" />
          <text v-else class="view-link">暂无图片</text>
        </view>
      </view>

      <!-- 设备类型 -->
      <view class="info-item">
        <text class="label">设备类型</text>
        <text class="value">{{ detail.categoryName }}</text>
      </view>

      <!-- 设备规格 -->
      <view class="info-item">
        <text class="label">设备规格</text>
        <text class="value">{{ detail.specifications }}</text>
      </view>

      <!-- 设备型号 -->
      <view class="info-item">
        <text class="label">设备型号</text>
        <text class="value">{{ detail.model }}</text>
      </view>

      <!-- 启用状态 -->
      <view class="info-item">
        <text class="label">启用状态</text>
        <text class="value">{{ formatOpenStatus(detail.open) }}</text>
      </view>

      <!-- 备注 -->
      <view class="info-item">
        <text class="label">备注</text>
        <text class="value">{{ detail.remark }}</text>
      </view>

      <!-- 库存数量 -->
      <view class="info-item">
        <text class="label">库存数量</text>
        <text class="value">{{ detail.stockNum }}</text>
      </view>

      <!-- 安装数量 -->
      <view class="info-item">
        <text class="label">安装数量</text>
        <text class="value">{{ detail.installNum }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BasicInfo',
}
</script>

<script setup>
// 定义props
const props = defineProps({
  detail: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 格式化启用状态
const formatOpenStatus = status => {
  if (status === null || status === undefined) {
    return 'xxxxxxxx'
  }
  return status === 1 ? '启用' : '禁用'
}
</script>

<style lang="scss" scoped>
.basic-info {
  padding: 0;
}

.loading-container {
  padding: 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.info-content {
  padding: 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 160rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.image-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex: 1;
}

.device-image {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
}

.view-link {
  font-size: 28rpx;
  color: #007aff;
  text-align: right;
}
</style>
