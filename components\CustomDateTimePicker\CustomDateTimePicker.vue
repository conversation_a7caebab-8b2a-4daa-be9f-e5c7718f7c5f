<template>
  <uni-popup ref="popup" type="bottom" @change="handlePopupChange">
    <view class="picker-container">
      <view class="picker-header">
        <text class="cancel-btn" @tap="handleCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm-btn" @tap="handleConfirm">确定</text>
      </view>
      <view class="picker-body">
        <picker-view class="picker-view" :value="currentValue" @change="handlePickerChange">
          <!-- 年份列 -->
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in yearsArray" :key="index">{{ item }}年</view>
          </picker-view-column>
          <!-- 月份列 -->
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in monthsArray" :key="index">{{ item }}月</view>
          </picker-view-column>
          <!-- 日期列 -->
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in daysArray" :key="index">{{ item }}日</view>
          </picker-view-column>
          <!-- 小时列 -->
          <picker-view-column v-if="showTime">
            <view class="picker-item" v-for="(item, index) in hoursArray" :key="index">{{ item }}时</view>
          </picker-view-column>
          <!-- 分钟列 -->
          <picker-view-column v-if="showTime">
            <view class="picker-item" v-for="(item, index) in minutesArray" :key="index">{{ item }}分</view>
          </picker-view-column>
          <!-- 秒钟列 -->
          <picker-view-column v-if="showTime && showSeconds">
            <view class="picker-item" v-for="(item, index) in secondsArray" :key="index">{{ item }}秒</view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'CustomDateTimePicker',
}
</script>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  // 显示状态
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 标题
  title: {
    type: String,
    default: '选择日期时间',
  },
  // 选中的值（Date对象或时间戳）
  value: {
    type: [Date, Number, String],
    default: () => new Date(),
  },
  // 格式化格式
  format: {
    type: String,
    default: 'yyyy-MM-dd HH:mm:ss',
  },
  // 最小年份
  minYear: {
    type: Number,
    default: 1970,
  },
  // 最大年份
  maxYear: {
    type: Number,
    default: () => new Date().getFullYear() + 10,
  },
  // 是否显示时间（时分秒）
  showTime: {
    type: Boolean,
    default: true,
  },
  // 是否显示秒
  showSeconds: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'update:value', 'change'])

const popup = ref(null)

// 生成年份数组
const yearsArray = computed(() => {
  const years = []
  for (let i = props.minYear; i <= props.maxYear; i++) {
    years.push(i)
  }
  return years
})

// 生成月份数组
const monthsArray = Array.from({ length: 12 }, (_, i) => i + 1)

// 生成日期数组（根据选中的年月动态计算）
const daysArray = computed(() => {
  const year = yearsArray.value[currentValue.value[0]]
  const month = monthsArray[currentValue.value[1]]
  const daysInMonth = new Date(year, month, 0).getDate()
  return Array.from({ length: daysInMonth }, (_, i) => i + 1)
})

// 生成小时数组
const hoursArray = Array.from({ length: 24 }, (_, i) => i)

// 生成分钟数组
const minutesArray = Array.from({ length: 60 }, (_, i) => i)

// 生成秒钟数组
const secondsArray = Array.from({ length: 60 }, (_, i) => i)

// 当前选中的值 [年份索引, 月份索引, 日期索引, 小时索引, 分钟索引, 秒钟索引]
const currentValue = ref([0, 0, 0, 0, 0, 0])

// 将Date对象或时间戳转换为选择器索引
function dateToPickerValue(date) {
  if (!date) return [0, 0, 0, 0, 0, 0]

  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return [0, 0, 0, 0, 0, 0]

  const year = dateObj.getFullYear()
  const month = dateObj.getMonth() + 1
  const day = dateObj.getDate()
  const hour = dateObj.getHours()
  const minute = dateObj.getMinutes()
  const second = dateObj.getSeconds()

  const yearIndex = yearsArray.value.indexOf(year)
  const monthIndex = monthsArray.indexOf(month)
  const dayIndex = day - 1

  return [
    yearIndex >= 0 ? yearIndex : 0,
    monthIndex >= 0 ? monthIndex : 0,
    dayIndex >= 0 ? dayIndex : 0,
    hour,
    minute,
    second,
  ]
}

// 将选择器索引转换为Date对象
function pickerValueToDate(pickerValue) {
  const year = yearsArray.value[pickerValue[0]] || yearsArray.value[0]
  const month = monthsArray[pickerValue[1]] || monthsArray[0]
  const day = daysArray.value[pickerValue[2]] || daysArray.value[0]
  const hour = props.showTime ? hoursArray[pickerValue[3]] || 0 : 0
  const minute = props.showTime ? minutesArray[pickerValue[4]] || 0 : 0
  const second = props.showTime && props.showSeconds ? secondsArray[pickerValue[5]] || 0 : 0

  return new Date(year, month - 1, day, hour, minute, second)
}

// 格式化日期
function formatDate(date, formatStr) {
  if (!date || isNaN(new Date(date).getTime())) return ''

  const dateObj = new Date(date)
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hour = String(dateObj.getHours()).padStart(2, '0')
  const minute = String(dateObj.getMinutes()).padStart(2, '0')
  const second = String(dateObj.getSeconds()).padStart(2, '0')

  return formatStr
    .replace(/yyyy/g, year)
    .replace(/MM/g, month)
    .replace(/dd/g, day)
    .replace(/HH/g, hour)
    .replace(/mm/g, minute)
    .replace(/ss/g, second)
}

// 监听value变化，更新currentValue
watch(
  () => props.value,
  newVal => {
    currentValue.value = dateToPickerValue(newVal)
  },
  { immediate: true }
)

// 监听显示状态
watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      // 打开时，根据当前value设置选中值
      currentValue.value = dateToPickerValue(props.value)
      popup.value.open('bottom')
    } else {
      popup.value.close()
    }
  }
)

// 监听年月变化，调整日期
watch(
  () => [currentValue.value[0], currentValue.value[1]],
  () => {
    // 当年月变化时，检查当前选中的日期是否超出该月的最大天数
    const maxDay = daysArray.value.length
    if (currentValue.value[2] >= maxDay) {
      currentValue.value[2] = maxDay - 1
    }
  }
)

// 处理选择器变化
function handlePickerChange(e) {
  const newValue = [...e.detail.value]

  // 检查日期是否超出当月最大天数
  const year = yearsArray.value[newValue[0]]
  const month = monthsArray[newValue[1]]
  const maxDay = new Date(year, month, 0).getDate()

  if (newValue[2] >= maxDay) {
    newValue[2] = maxDay - 1
  }

  currentValue.value = newValue
  // 移除实时更新，只在内部更新选择器状态
  // const selectedDate = pickerValueToDate(newValue)
  // emit('change', selectedDate)
}

// 取消选择
function handleCancel() {
  // 重置为原来的值
  currentValue.value = dateToPickerValue(props.value)
  popup.value.close()
  emit('update:modelValue', false)
}

// 确认选择
function handleConfirm() {
  const selectedDate = pickerValueToDate(currentValue.value)
  emit('update:value', selectedDate)
  emit('change', selectedDate) // 只在确认时触发change事件
  popup.value.close()
  emit('update:modelValue', false)
}

// 处理弹窗状态变化
function handlePopupChange(e) {
  // 当弹窗关闭时，同步更新 modelValue
  if (e.show === false) {
    emit('update:modelValue', false)
  }
}

// 暴露方法给父组件
defineExpose({
  formatDate,
})
</script>

<style lang="scss" scoped>
.picker-container {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.cancel-btn {
  font-size: 32rpx;
  color: #999;
}

.confirm-btn {
  font-size: 32rpx;
  color: #2979ff;
}

.picker-body {
  height: 500rpx;
}

.picker-view {
  width: 100%;
  height: 100%;
}

.picker-item {
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}
</style>
