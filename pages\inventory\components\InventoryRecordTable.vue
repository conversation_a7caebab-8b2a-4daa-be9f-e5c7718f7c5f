<template>
  <view class="record-table">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 表格内容 -->
    <view v-else class="table-container">
      <uni-table border stripe>
        <!-- 表头 -->
        <uni-tr class="table-header">
          <uni-th width="140" align="center">
            <view class="header-cell">
              <text>操作时间</text>
              <uni-icons type="arrowdown" size="12" color="#999" />
            </view>
          </uni-th>
          <uni-th width="100" align="center">
            <view class="header-cell">
              <text>操作类型</text>
              <uni-icons type="arrowdown" size="12" color="#999" />
            </view>
          </uni-th>
          <uni-th width="80" align="center">数量</uni-th>
          <uni-th width="100" align="center">操作后库存</uni-th>
          <uni-th width="120" align="center">备注</uni-th>
        </uni-tr>

        <!-- 表格数据 -->
        <uni-tr v-for="(item, index) in displayData" :key="index" class="table-row">
          <uni-td width="140" align="center">{{ formatTime(item.createTime) }}</uni-td>
          <uni-td width="100" align="center">
            <view class="operation-type" :class="getOperationTypeClass(item.operate)">
              {{ getOperationTypeName(item.operate) }}
            </view>
          </uni-td>
          <uni-td width="80" align="center">{{ item.number }}</uni-td>
          <uni-td width="100" align="center">{{ item.nowNumber }}</uni-td>
          <uni-td width="120" align="center">{{ item.remark }}</uni-td>
        </uni-tr>

        <!-- 空数据提示 -->
        <uni-tr v-if="!loading && displayData.length === 0">
          <uni-td colspan="5" align="center" class="empty-data">
            <text>暂无数据</text>
          </uni-td>
        </uni-tr>
      </uni-table>
    </view>
  </view>
</template>

<script>
export default {
  name: 'InventoryRecordTable',
}
</script>

<script setup>
import { computed } from 'vue'

// 定义props
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 显示数据
const displayData = computed(() => {
  return props.data
})

// 格式化时间
const formatTime = time => {
  if (!time) return ''
  // 这里可以根据实际需要格式化时间
  return time
}

// 获取操作类型名称
const getOperationTypeName = type => {
  const typeMap = {
    in: '入库',
    out: '出库',
    adjust: '库存调整',
  }
  return typeMap[type] || type || '入库'
}

// 获取操作类型样式类
const getOperationTypeClass = type => {
  const classMap = {
    in: 'type-in',
    out: 'type-out',
    adjust: 'type-adjust',
  }
  return classMap[type] || 'type-in'
}
</script>

<style lang="scss" scoped>
.record-table {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-container {
  padding: 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.table-container {
  padding: 20rpx;
  height: 100%;
  overflow-y: auto;
}

.table-header {
  background-color: #f8f9fa;

  :deep(.uni-table-th) {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    padding: 24rpx 16rpx;
  }
}

.header-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.table-row {
  :deep(.uni-table-td) {
    font-size: 26rpx;
    color: #666;
    padding: 24rpx 16rpx;
  }
}

.operation-type {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.type-in {
    background-color: #e8f5e8;
    color: #52c41a;
    border: 1rpx solid #b7eb8f;
  }

  &.type-out {
    background-color: #fff2e8;
    color: #fa8c16;
    border: 1rpx solid #ffd591;
  }

  &.type-adjust {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1rpx solid #91d5ff;
  }
}

.empty-data {
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}

// 表格样式优化
:deep(.uni-table) {
  border-radius: 12rpx;
  overflow: hidden;

  .uni-table-tr {
    &:hover {
      background-color: #f8f9fa;
    }
  }

  .uni-table-th,
  .uni-table-td {
    border-color: #e9ecef;
  }
}
</style>
