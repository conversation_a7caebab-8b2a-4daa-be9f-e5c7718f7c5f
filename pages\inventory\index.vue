<template>
  <view class="inventory-container">
    <TabFilter
      v-model="searchParams.fatherId"
      :tabs="device_category"
      @tab-change="handleTabChange"
      @filter-click="handleFilterClick"
    ></TabFilter>
    <view class="content-container">
      <CustomSwipeCard
        class="record-item"
        v-if="inventoryList.length > 0"
        v-for="(item, index) in inventoryList"
        :key="item.id"
        :currentData="item"
        :customSwipeOptions="getSwipeOptions(item.open)"
        @swipe-click="handleRecordClick"
        @card-click="handleRecordDetail(item)"
      >
        <template #content>
          <view class="record-item-content">
            <view v-if="getStatusImage(item.open)" class="inventory-status-container">
              <image class="inventory-status-tag" :src="getStatusImage(item.open)" mode="scaleToFill" />
              <text class="inventory-status-text">{{ getStatusText(item.open) }}</text>
            </view>
            <view class="title">
              <image src="/static/images/maintenance/green.png" mode="scaleToFill" />
              <view class="title-text">{{ item.deviceName }}</view>
              <text class="device-type">{{ item.categoryName }}</text>
            </view>
            <view class="detail">
              <view class="detail-item">
                <label class="label">设备规格</label>
                <text class="value">{{ item.specifications || '--' }}</text>
              </view>
              <view class="detail-item">
                <label class="label">设备型号</label>
                <text class="value">{{ item.model || '--' }}</text>
              </view>
              <view class="detail-item">
                <label class="label">库存数量</label>
                <text class="value" :class="getStockClass(item.stockNum)">{{ item.stockNum || 0 }}</text>
              </view>
              <view class="detail-item">
                <label class="label">安装数量</label>
                <text class="value">{{ item.installNum || 0 }}</text>
              </view>
            </view>
          </view>
        </template>
      </CustomSwipeCard>
      <EmptyData v-else text="暂无库存数据" />
    </view>
  </view>
  <!-- 筛选弹窗 -->
  <FilterPopup v-model="showFilterPopup" :defaultParams="searchParams" @confirm="handleFilterConfirm" />
</template>
<script setup>
import { ref, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import TabFilter from '@/components/TabFilter/TabFilter.vue'
import CustomSwipeCard from '@/components/CustomSwipeCard/CustomSwipeCard.vue'
import EmptyData from '@/components/EmptyData/EmptyData.vue'
import FilterPopup from './components/filterPopup.vue'
import { getInventoryList } from '@/api/inventory'

const { proxy } = getCurrentInstance()

const { device_category } = proxy.useTypeList('device_category')

// device_category: [
//   {
//     label: '摄像头',
//     value: '0'
//   },
//   {
//     label: '探头',
//     value: '1'
//   },
//   {
//     label: '配件',
//     value: '2'
//   },
//   {
//     label: '其他',
//     value: '3'
//   }
// ],

// 滑动选项配置
const configMap = {
  // 已启用状态
  1: [
    {
      text: '修改库存',
      style: {
        backgroundColor: '#2979ff',
      },
    },
  ],
  // 已禁用状态
  0: [
    {
      text: '修改库存',
      style: {
        backgroundColor: '#2979ff',
      },
    },
  ],
}

// 状态图片映射
const statusImageMap = {
  1: '/static/images/success-tag.png', // 已启用 - 绿色
  0: '/static/images/error-tag.png', // 已禁用 - 红色
}

// 状态文本映射
const statusTextMap = {
  1: '已启用',
  0: '已禁用',
}

const searchParams = ref({
  deviceName: '', // 设备名称
  fatherId: '0', // 分类id，默认为摄像头
  model: '', // 设备型号
  specifications: '', // 设备规格
  type: [], // 点筛选去掉的类型id列表
})

const inventoryList = ref([])
const showFilterPopup = ref(false) // 控制筛选弹窗显示

// 根据当前状态获取对应的滑动操作按钮
function getSwipeOptions(open) {
  return configMap[open] || []
}

// 根据状态获取对应的状态图片
function getStatusImage(open) {
  return statusImageMap[open] || ''
}

// 根据状态获取状态文本
function getStatusText(open) {
  return statusTextMap[open] || (open === 1 ? '已启用' : '已禁用')
}

// 根据库存数量获取对应的样式类
function getStockClass(quantity) {
  const num = Number(quantity)
  if (num <= 5) return 'stock-low'
  if (num <= 10) return 'stock-medium'
  return 'stock-normal'
}

function handleRecordDetail(item) {
  proxy.$tab.navigateTo(`/pages/inventory/inventoryDetail?id=${item.id}&mode=view`)
}

function handleRecordClick(e) {
  const {
    option: { text },
    currentData,
  } = e

  const actionHandlers = {
    修改库存: () => {
      // 数据使用 全局缓存处理
      uni.setStorageSync('currentInventory', currentData)
      proxy.$tab.navigateTo(`/pages/inventory/editInventory?itemId=${currentData.id}`)
    },
  }

  // 执行对应的处理函数，如果不存在则提示不支持的操作
  if (actionHandlers[text]) {
    actionHandlers[text]()
  } else {
    proxy.$modal.msg(`不支持的操作: ${text}`)
  }
}

function handleTabChange() {
  getInventoryData()
}

// 处理筛选按钮点击事件
function handleFilterClick() {
  showFilterPopup.value = true
}

// 处理筛选确认
function handleFilterConfirm(params) {
  // 更新搜索参数
  Object.assign(searchParams.value, params)
  // 重新获取数据
  getInventoryData()
}

async function getInventoryData() {
  try {
    proxy.$modal.loading('加载中...')
    // 获取库存列表
    const res = await getInventoryList(searchParams.value)
    inventoryList.value = res.data
  } catch (error) {
    console.error('获取库存数据失败:', error)
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 页面加载
onMounted(() => {
  getInventoryData()

  // 监听刷新列表事件
  uni.$on('refreshInventoryList', () => {
    // 刷新列表数据
    getInventoryData()
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshInventoryList')
})
</script>
<style lang="scss" scoped>
.inventory-container {
  display: flex;
  flex-direction: column;
  height: $uni-height-area;
  background-color: #f5f5f5;
}

.content-container {
  position: relative;
  flex: 1;
  overflow-y: scroll;
  min-height: 0;
  padding: 0 30rpx;
  margin-top: 30rpx;
  background-color: #f5f5f5;

  .record-item {
    margin-bottom: 30rpx;
  }
}

.record-item-content {
  padding: 54rpx 0 42rpx 64rpx;
  position: relative;

  .inventory-status-container {
    position: absolute;
    display: inline-flex;
    top: 0;
    right: 0;

    .inventory-status-tag {
      width: 170rpx;
      height: 66rpx;
    }

    .inventory-status-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 28rpx;
      font-weight: 500;
      color: #fff;
    }
  }

  .title {
    display: flex;
    align-items: center;
    margin-bottom: 50rpx;

    image {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }

    .title-text {
      font-size: 38rpx;
      font-weight: 500;
      color: #000;
      margin-right: 20rpx;
    }

    .device-type {
      font-size: 24rpx;
      color: #2979ff;
      background-color: #fff;
      border: 1px solid #a3c4ff;
      padding: 6rpx 12rpx;
      border-radius: 8rpx;
      font-weight: 600;
    }
  }

  .detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    .detail-item {
      .label {
        color: #7d7d91;
        font-size: 32rpx;
        font-weight: 400;
        margin-right: 20rpx;
      }

      .value {
        color: #222631;
        font-size: 32rpx;
        font-weight: 400;

        &.stock-low {
          color: #ce4427;
        }

        &.stock-medium {
          color: #ea9d4a;
        }

        &.stock-normal {
          color: #27a376;
        }
      }
    }
  }
}
</style>
