<!-- 填写维保订单 -->
<template>
  <view class="maintenance-records-container">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 订单类型 -->
      <view class="form-item">
        <view class="form-label required">
          <text>订单类型</text>
        </view>
        <view class="form-value">
          <text class="form-text">采购</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 紧急程度 -->
      <view class="form-item">
        <view class="form-label required">
          <text>紧急程度</text>
        </view>
        <view class="form-value" @tap="openUrgencyPicker">
          <text v-if="formData.maintenOrderUrgency" class="form-text">{{
            urgency_state.find(item => item.value === formData.maintenOrderUrgency)?.label
          }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 开始时间 -->
      <view class="form-item">
        <view class="form-label required">
          <text>开始时间</text>
        </view>
        <view class="form-value" @tap="showStartTimePicker = true">
          <text v-if="formData.startTime" class="form-text">{{ formattedStartTime }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 采购期限 -->
      <view class="form-item">
        <view class="form-label required">
          <text>采购期限</text>
        </view>
        <view class="form-value" @tap="openMaintenanceTimePicker">
          <text v-if="formData.maintenPeriod" class="form-text">{{ formatDuration(formData.maintenPeriod) }}</text>
          <text v-else class="form-placeholder">请选择</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view>

      <!-- 备注 -->
      <view class="form-item">
        <view class="form-label">
          <text>备注</text>
        </view>
        <view class="form-value">
          <input
            type="text"
            v-model="formData.remark"
            placeholder="请输入"
            placeholder-class="form-placeholder"
            class="form-input"
          />
        </view>
      </view>

      <!-- 选择设备 -->
      <view class="form-item device-select-container">
        <view class="form-label required">
          <text>选择设备</text>
        </view>
        <view class="device-list">
          <view class="device-item add-device" @tap="openDeviceSelector">
            <uni-icons type="plusempty" size="30" color="#2979ff"></uni-icons>
            <text class="add-text">选择设备</text>
          </view>
          <view
            v-for="(device, index) in formData.deviceList"
            :key="device.deviceId"
            class="device-item device-preview"
          >
            <text class="device-name">{{ device.deviceName }}</text>
            <view class="device-delete" @tap.stop="removeDevice(index)">
              <text class="device-delete-x">×</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn-container">
      <button class="draft-btn" @tap="saveDraft">保存为草稿</button>
      <button class="submit-btn" @tap="submitForm">提交</button>
    </view>
  </view>

  <!-- 紧急程度选择器 -->
  <CustomActionSheet
    v-model="showUrgency"
    v-model:value="formData.maintenOrderUrgency"
    :options="urgency_state"
    title="紧急程度"
  />

  <!-- 维保期限选择器 -->
  <CustomTimePicker
    v-model="showMaintenanceTime"
    v-model:value="formData.maintenPeriod"
    :max-hours="9999"
    title="采购期限"
    ref="timePickerRef"
  />

  <!-- 设备选择器 -->
  <DeviceSelectorPopup v-model="showDeviceSelector" :type="deviceSelectorType" @confirm="handleDeviceSelected" />

  <!-- 开始时间选择器 -->
  <CustomDateTimePicker
    v-model="showStartTimePicker"
    v-model:value="startTimeValue"
    title="选择开始时间"
    format="yyyy-MM-dd HH:mm:ss"
    :show-time="true"
    :show-seconds="false"
    @change="handleStartTimeChange"
  />
</template>

<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomActionSheet from '@/components/CustomActionSheet/CustomActionSheet.vue'
import CustomTimePicker from '@/components/CustomTimePicker/CustomTimePicker.vue'
import CustomDateTimePicker from '@/components/CustomDateTimePicker/CustomDateTimePicker.vue'
import DeviceSelectorPopup from './components/deviceSelectorPopup.vue'
import { addMaintenanceRecords, getMaintenanceRecordDetail } from '@/api/maintenance'
import { formatDuration } from '@/utils/formatUtils'

// 获取当前实例上下文
const { proxy } = getCurrentInstance()

// 表单数据
const formData = ref({
  type: '3', // 订单类型

  maintenOrderUrgency: '', // 紧急程度
  startTime: '', // 开始时间
  maintenPeriod: 0, // 维保期限（秒数）
  remark: '', // 备注
  deviceList: [], // 设备列表
})

const id = ref('')

// 选择器状态
const showUrgency = ref(false)
const showMaintenanceTime = ref(false)
const showStartTimePicker = ref(false)
const timePickerRef = ref(null)

// 开始时间值
const startTimeValue = ref(new Date())

// 已选设备列表
const selectedDevices = ref([])

// 设备选择器
const showDeviceSelector = ref(false)
const deviceSelectorType = ref('add') // 'add', 'edit', 'remove'

// 选项数据

const { urgency_state } = proxy.useDict('urgency_state')

// 打开紧急程度选择器
function openUrgencyPicker() {
  showUrgency.value = true
}

// 打开维保期限选择器
function openMaintenanceTimePicker() {
  showMaintenanceTime.value = true
}

// 格式化日期时间显示
const formatDateTime = (date, format = 'yyyy-MM-dd HH:mm:ss') => {
  if (!date || isNaN(new Date(date).getTime())) return ''

  const dateObj = new Date(date)
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hour = String(dateObj.getHours()).padStart(2, '0')
  const minute = String(dateObj.getMinutes()).padStart(2, '0')
  const second = String(dateObj.getSeconds()).padStart(2, '0')

  return format
    .replace(/yyyy/g, year)
    .replace(/MM/g, month)
    .replace(/dd/g, day)
    .replace(/HH/g, hour)
    .replace(/mm/g, minute)
    .replace(/ss/g, second)
}

// 计算属性 - 格式化后的开始时间
const formattedStartTime = computed(() => {
  return formData.value.startTime ? formatDateTime(formData.value.startTime, 'yyyy-MM-dd HH:mm') : ''
})

// 处理开始时间变化
function handleStartTimeChange(date) {
  formData.value.startTime = formatDateTime(date, 'yyyy-MM-dd HH:mm:ss')
}

async function getRecordDetail() {
  proxy.$modal.loading('加载中...')
  try {
    const res = await getMaintenanceRecordDetail({ id: id.value })
    formData.value = res.data
  } catch (error) {
    proxy.$modal.msgError(error.message)
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 保存为草稿
function saveDraft() {
  proxy.$modal.loading('保存中...')
  formData.value.state = 3
  // 确保时间包含秒位
  const dataToSubmit = { ...formData.value, startTime: formattedStartTime.value }

  addMaintenanceRecords(dataToSubmit)
    .then(res => {
      proxy.$modal.msgSuccess('已保存为草稿')
      setTimeout(() => {
        // 触发刷新事件，通知列表页刷新数据
        uni.$emit('refreshPurchaseList')
        proxy.$tab.navigateBack()
      }, 1000)
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

// 打开设备选择器
function openDeviceSelector() {
  if (formData.value.type == '') {
    proxy.$modal.msg('请选择订单类型')
    return
  }
  let typeMap = {
    0: 'add',
    1: 'edit',
    2: 'remove',
  }
  deviceSelectorType.value = typeMap[Number(formData.value.type)]
  showDeviceSelector.value = true
}

// 移除已选设备
function removeDevice(index) {
  formData.value.deviceList.splice(index, 1)
}

// 处理设备选择确认
function handleDeviceSelected(device) {
  // 避免重复添加
  const exists = formData.value.deviceList.some(
    item => item.deviceId == device.deviceId && item.pointId == device.pointId
  )

  if (!exists) {
    // 更新设备列表
    formData.value.deviceList.push(device)
  }
}

// 表单验证
function validateForm() {
  if (!formData.value.type) {
    proxy.$modal.msg('请选择订单类型')
    return false
  }
  if (!formData.value.maintenOrderUrgency) {
    proxy.$modal.msg('请选择紧急程度')
    return false
  }
  if (!formData.value.startTime) {
    proxy.$modal.msg('请选择开始时间')
    return false
  }
  if (!formData.value.maintenPeriod) {
    proxy.$modal.msg('请选择维保期限')
    return false
  }
  if (formData.value.deviceList.length === 0) {
    proxy.$modal.msg('请选择设备')
    return false
  }
  return true
}

// 提交表单
function submitForm() {
  if (!validateForm()) return
  // 确保时间包含秒位
  const dataToSubmit = { ...formData.value, startTime: formattedStartTime.value, state: 1 }
  proxy.$modal.loading('提交中...')
  addMaintenanceRecords(dataToSubmit)
    .then(res => {
      proxy.$modal.msgSuccess('提交成功')

      // 提交成功后可以跳转到列表页
      setTimeout(() => {
        // 触发刷新事件，通知列表页刷新数据
        uni.$emit('refreshPurchaseList')
        proxy.$tab.navigateBack()
      }, 1000)
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

onLoad(async option => {
  if (option.type == 'Draft') {
    id.value = option.id
    await getRecordDetail()
  }
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.maintenance-records-container {
  display: flex;
  flex-direction: column;
  height: $uni-height-area;
  background-color: #f5f5f5;
  padding: 30rpx;
}

.form-container {
  background-color: #fff;
  padding: 0 30rpx;
  flex: 1;
  overflow-y: auto;
  border-radius: 20rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.required::before {
  content: '*';
  color: #ff0000;
  margin-right: 4rpx;
}

.form-value {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.datetime-picker-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;

  :deep(.uni-date) {
    width: 100%;

    .uni-date-editor {
      width: 100%;
      border: none !important;
      padding: 0;
    }

    .uni-date-x {
      border: none !important;
    }
  }
}

.datetime-display {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.form-text {
  font-size: 32rpx;
  color: #333;
  margin-right: 10rpx;
}

.form-placeholder {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}

.form-input {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  width: 100%;
}

.submit-btn-container {
  padding: 0;
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
}

.draft-btn,
.submit-btn {
  height: 90rpx;
  font-size: 36rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.draft-btn {
  width: 30%;
  background-color: #fff;
  color: #333;
}

.submit-btn {
  width: 65%;
  background-color: #2979ff;
  color: #fff;
  border: none;
}

.device-select-container {
  flex-direction: column;
  align-items: flex-start;
}

.device-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  width: 100%;
}

.device-item {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.add-device {
  border: 1px dashed #ddd;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
}

.add-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.device-preview {
  background-image: url('/static/images/maintenance/device.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2), 0 3rpx 8rpx rgba(0, 0, 0, 0.15), inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.device-name {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
  padding: 10rpx;
  text-align: center;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  overflow: hidden;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.5);
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 6rpx;
  margin: 10rpx;
}

.device-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 50rpx;
  height: 50rpx;
  z-index: 10;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.device-delete::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 50rpx 50rpx 0;
  border-color: transparent rgba(0, 0, 0, 0.6) transparent transparent;
  transition: all 0.2s ease;
}

.device-delete:active::before {
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
}

.device-delete:active {
  transform: scale(0.95);
}

.device-delete-x {
  position: relative;
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
  line-height: 1;
  pointer-events: none;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  margin-top: -16rpx;
  margin-right: -16rpx;
}
</style>
