<template>
  <CustomPopup
    title="筛选条件"
    :modelValue="visible"
    @update:modelValue="updateVisible"
    @confirm="handleConfirm"
    @reset="handleReset"
    resetText="重置筛选条件"
    confirmText="确定"
  >
    <!-- 设备名称 -->
    <view class="filter-item">
      <text class="filter-label">设备名称</text>
      <view class="filter-input-container">
        <input
          type="text"
          v-model="filterParams.deviceName"
          class="filter-input"
          placeholder="请输入设备名称"
          placeholder-class="placeholder"
        />
      </view>
    </view>

    <!-- 设备类型 -->
    <view class="filter-item">
      <text class="filter-label">设备类型</text>
      <view class="filter-types">
        <view
          class="type-item"
          :class="{ active: filterParams.type.includes(type.value) }"
          v-for="type in deviceTypeOptions"
          :key="type.value"
          @tap="setDeviceType(type.value)"
        >
          {{ type.label }}
        </view>
      </view>
    </view>

    <!-- 设备型号 -->
    <view class="filter-item">
      <text class="filter-label">设备型号</text>
      <view class="filter-input-container">
        <input
          type="text"
          v-model="filterParams.model"
          class="filter-input"
          placeholder="请输入设备型号"
          placeholder-class="placeholder"
        />
      </view>
    </view>

    <!-- 设备规格 -->
    <view class="filter-item">
      <text class="filter-label">设备规格</text>
      <view class="filter-input-container">
        <input
          type="text"
          v-model="filterParams.specifications"
          class="filter-input"
          placeholder="请输入设备规格"
          placeholder-class="placeholder"
        />
      </view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { ref, reactive, computed, watch, getCurrentInstance, onMounted } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
import { sonDeviceType } from '@/api/device'
// 获取当前实例上下文
const { proxy } = getCurrentInstance()

const props = defineProps({
  // 控制弹窗显示状态
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 初始筛选参数
  defaultParams: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 弹窗可见性
const visible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

// 更新弹窗可见性
const updateVisible = val => {
  visible.value = val
}

// 设备类型选项
const deviceTypeOptions = ref([])

// 筛选参数
const filterParams = reactive({
  deviceName: '', // 设备名称
  fatherId: '', // 分类id
  model: '', // 设备型号
  specifications: '', // 设备规格
  type: [], // 点筛选去掉的类型id列表
})

// 设置设备类型
const setDeviceType = type => {
  const index = filterParams.type.indexOf(type)
  if (index > -1) {
    // 如果已选中，则移除
    filterParams.type.splice(index, 1)
  } else {
    // 如果未选中，则添加
    filterParams.type.push(type)
  }
}

// 确认筛选
const handleConfirm = () => {
  emit('confirm', { ...filterParams })
}

// 重置筛选
const handleReset = () => {
  // 重置所有筛选参数
  filterParams.deviceName = ''
  filterParams.fatherId = ''
  filterParams.model = ''
  filterParams.specifications = ''
  filterParams.type = []
}

// 获取设备类型列表
const getDeviceTypes = async () => {
  if (!props.defaultParams.fatherId) {
    deviceTypeOptions.value = []
    return
  }

  try {
    // 调用API根据设备类别ID获取设备类型列表
    const res = await sonDeviceType({ fatherId: props.defaultParams.fatherId })
    deviceTypeOptions.value = res.data.map(item => {
      return {
        label: item.dictLabel,
        value: item.dictValue,
      }
    })
  } catch (error) {
    console.error('获取设备类型失败:', error)

    deviceTypeOptions.value = []
  }
}

// 初始化数据
const initData = () => {
  if (props.defaultParams) {
    // 合并默认参数
    Object.assign(filterParams, props.defaultParams)
  }
}

// 监听设备类别ID变化
watch(
  () => props.defaultParams.fatherId,
  newId => {
    if (newId) {
      getDeviceTypes()
      // 重置设备类型选择
      filterParams.type = []
    }
  },
  { immediate: true }
)

// 初始化
initData()
</script>

<style lang="scss" scoped>
.filter-item {
  margin-bottom: 30rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-input-container {
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
}

.filter-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}

.stock-range {
  display: flex;
  align-items: center;
  gap: 20rpx;

  .filter-input-container {
    flex: 1;
  }

  .separator {
    color: #999;
    font-size: 28rpx;
  }
}

.filter-types {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.type-item {
  margin: 10rpx;
  padding: 0 30rpx;
  height: 70rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;

  &.active {
    border-color: #2979ff;
    background-color: rgba(41, 121, 255, 0.1);
    color: #2979ff;
  }
}
</style>
