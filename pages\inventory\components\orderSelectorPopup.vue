<template>
  <CustomPopup
    title="选择维保订单"
    :modelValue="visible"
    @update:modelValue="updateVisible"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    confirmText="确定"
    cancelText="取消"
    :showFooter="selectedOrder.id"
  >
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-input-container">
        <input
          type="text"
          v-model="searchKeyword"
          class="search-input"
          placeholder="搜索订单号或设备名称"
          placeholder-class="placeholder"
          @input="handleSearch"
        />
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <view 
        class="order-item"
        :class="{ active: selectedOrder.id === order.id }"
        v-for="order in filteredOrders"
        :key="order.id"
        @tap="selectOrder(order)"
      >
        <view class="order-header">
          <text class="order-code">{{ order.orderCode }}</text>
          <view class="order-status" :class="getStatusClass(order.status)">
            {{ getStatusText(order.status) }}
          </view>
        </view>
        <view class="order-info">
          <text class="device-name">{{ order.deviceName }}</text>
          <text class="create-time">{{ order.createTime }}</text>
        </view>
        <view class="order-desc" v-if="order.description">
          {{ order.description }}
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredOrders.length === 0">
        <text class="empty-text">{{ searchKeyword ? '未找到相关订单' : '暂无可选订单' }}</text>
      </view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance, onMounted } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
import { getMaintenanceOrders } from '@/api/maintenance'

const { proxy } = getCurrentInstance()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 弹窗可见性
const visible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

// 更新弹窗可见性
const updateVisible = val => {
  visible.value = val
}

// 搜索关键词
const searchKeyword = ref('')

// 订单列表
const orderList = ref([])

// 选中的订单
const selectedOrder = ref({})

// 状态映射
const statusMap = {
  pending: { text: '待处理', class: 'pending' },
  processing: { text: '处理中', class: 'processing' },
  completed: { text: '已完成', class: 'completed' },
}

// 过滤后的订单列表
const filteredOrders = computed(() => {
  if (!searchKeyword.value) {
    return orderList.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return orderList.value.filter(order => 
    order.orderCode.toLowerCase().includes(keyword) ||
    order.deviceName.toLowerCase().includes(keyword)
  )
})

// 获取状态文本
const getStatusText = (status) => {
  return statusMap[status]?.text || status
}

// 获取状态样式类
const getStatusClass = (status) => {
  return statusMap[status]?.class || ''
}

// 选择订单
const selectOrder = (order) => {
  selectedOrder.value = order
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
}

// 确认选择
const handleConfirm = () => {
  if (selectedOrder.value.id) {
    emit('confirm', selectedOrder.value)
    handleCancel()
  } else {
    proxy.$modal.msg('请选择一个订单')
  }
}

// 取消选择
const handleCancel = () => {
  selectedOrder.value = {}
  searchKeyword.value = ''
  visible.value = false
}

// 获取维保订单列表
const getOrderList = async () => {
  try {
    // 调用API获取可选的维保订单
    const res = await getMaintenanceOrders({
      status: 'pending,processing', // 只获取待处理和处理中的订单
      pageSize: 100
    })
    orderList.value = res.data || []
  } catch (error) {
    console.error('获取维保订单失败:', error)
    // 使用模拟数据
    orderList.value = [
      {
        id: '1',
        orderCode: 'WB202401001',
        deviceName: '摄像头设备A',
        status: 'pending',
        createTime: '2024-01-15 10:30:00',
        description: '设备故障维修'
      },
      {
        id: '2',
        orderCode: 'WB202401002',
        deviceName: '探头设备B',
        status: 'processing',
        createTime: '2024-01-16 14:20:00',
        description: '定期保养维护'
      },
      {
        id: '3',
        orderCode: 'WB202401003',
        deviceName: '摄像头设备C',
        status: 'pending',
        createTime: '2024-01-17 09:15:00',
        description: '设备升级维护'
      }
    ]
  }
}

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    getOrderList()
  }
})

// 页面加载
onMounted(() => {
  // 初始化时不加载数据，等弹窗打开时再加载
})
</script>

<style lang="scss" scoped>
.search-container {
  margin-bottom: 30rpx;

  .search-input-container {
    height: 80rpx;
    border: 1px solid #e5e5e5;
    border-radius: 12rpx;
    padding: 0 24rpx;
    display: flex;
    align-items: center;
    background-color: #f8f9fa;

    .search-input {
      flex: 1;
      height: 100%;
      font-size: 28rpx;
      color: #333;
    }

    .placeholder {
      color: #999;
      font-size: 28rpx;
    }
  }
}

.order-list {
  max-height: 800rpx;
  overflow-y: auto;

  .order-item {
    padding: 30rpx;
    border: 1px solid #e5e5e5;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    transition: all 0.3s;

    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      border-color: #2979ff;
      background-color: rgba(41, 121, 255, 0.05);
    }

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15rpx;

      .order-code {
        font-size: 32rpx;
        font-weight: 600;
        color: #000;
      }

      .order-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.pending {
          background-color: rgba(255, 193, 7, 0.1);
          color: #ffc107;
        }

        &.processing {
          background-color: rgba(41, 121, 255, 0.1);
          color: #2979ff;
        }

        &.completed {
          background-color: rgba(40, 167, 69, 0.1);
          color: #28a745;
        }
      }
    }

    .order-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10rpx;

      .device-name {
        font-size: 28rpx;
        color: #666;
      }

      .create-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .order-desc {
      font-size: 26rpx;
      color: #999;
      line-height: 1.4;
    }
  }

  .empty-state {
    text-align: center;
    padding: 100rpx 0;

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }
}
</style>
