import useDictStore from "@/store/modules/dict"
import { getDicts } from "@/api/system/dict/data"
import { ref, toRefs } from "vue"

/**
 * 获取字典数据
 */
export function useDict(...args) {
  const res = ref({})
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = []
      const dicts = useDictStore().getDict(dictType)
      if (dicts) {
        res.value[dictType] = dicts
      } else {
        getDicts(dictType).then((resp) => {
          res.value[dictType] = resp.data.map((p) => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass, elTagClass: p.cssClass }))
          useDictStore().setDict(dictType, res.value[dictType])
        })
      }
    })
    return toRefs(res.value)
  })()
}


export const typeList = {
  // 设备类型（如果这里的类型更改，需要到 views/device/camera 文件下中同步修改）
  device_type: [
    {
      label: '摄像头',
      value: '0'
    },
    {
      label: '探头',
      value: '1'
    }
  ],
  device_category: [
    {
      label: '摄像头',
      value: '0'
    },
    {
      label: '探头',
      value: '1'
    },
    {
      label: '配件',
      value: '2'
    },
    {
      label: '其他',
      value: '3'
    }
  ],
  category_type: [
    {
      label: '温度',
      value: '0'
    },
    {
      label: '烟雾浓度',
      value: '1'
    },
    {
      label: '湿度',
      value: '2'
    },
    {
      label: '气体浓度',
      value: '3'
    }
  ],
  // 库存类型（前后端写死，修改需要和后端同步）
  stock_type: [
    {
      label: '库存调整',
      value: '0'
    },
    {
      label: '出库',
      value: '1'
    },
    {
      label: '入库',
      value: '2'
    }
  ],
  // 订单状态
  maintenance_state: [
    {
      label: '未完成',
      value: '0',
      elTagType: 'danger'
    },
    {
      label: '已完成',
      value: '1',
      elTagType: 'info'
    },
    {
      label: '已作废',
      value: '2',
      elTagType: 'warning'
    }
  ]
}

/** 获取自定义的类型数据 */
export function useTypeList(...args) {
  let res = {}
  return (() => {
    args.forEach((dictType, index) => {
      res[dictType] = typeList[dictType]
    })
    return res
  })()
}