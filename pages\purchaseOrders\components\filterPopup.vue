<template>
  <CustomPopup
    title="筛选条件"
    :modelValue="visible"
    @update:modelValue="updateVisible"
    @confirm="handleConfirm"
    @reset="handleReset"
    resetText="重置筛选条件"
    confirmText="确定"
  >
    <!-- 订单编号 -->
    <view class="filter-item">
      <text class="filter-label">订单编号</text>
      <view class="filter-input-container">
        <input
          type="text"
          v-model="filterParams.orderCode"
          class="filter-input"
          placeholder="请输入采购订单号"
          placeholder-class="placeholder"
        />
      </view>
    </view>

    <!-- 开始时间范围 -->
    <view class="filter-item">
      <text class="filter-label">开始时间范围</text>
      <view class="time-range">
        <view class="datetime-picker" @tap="showStartTimePicker = true">
          <view class="filter-input-container">
            <text class="datetime-text">{{ formattedStartTime || '请选择开始时间' }}</text>
          </view>
        </view>
        <text class="separator">至</text>
        <view class="datetime-picker" @tap="showEndTimePicker = true">
          <view class="filter-input-container">
            <text class="datetime-text">{{ formattedEndTime || '请选择结束时间' }}</text>
          </view>
        </view>
      </view>
      <view class="clear-btn-container" v-if="filterParams.startTimeStart || filterParams.startTimeEnd">
        <text class="clear-btn" @tap="clearStartTimeRange">清除</text>
      </view>
    </view>

    <!-- 结束时间范围 -->
    <view class="filter-item">
      <text class="filter-label">结束时间范围</text>
      <view class="time-range">
        <view class="datetime-picker" @tap="showEndTimeStartPicker = true">
          <view class="filter-input-container">
            <text class="datetime-text">{{ formattedEndTimeStart || '请选择结束时间' }}</text>
          </view>
        </view>
        <text class="separator">至</text>
        <view class="datetime-picker" @tap="showEndTimeEndPicker = true">
          <view class="filter-input-container">
            <text class="datetime-text">{{ formattedEndTimeEnd || '请选择结束时间' }}</text>
          </view>
        </view>
      </view>
      <view class="clear-btn-container" v-if="filterParams.endTimeStart || filterParams.endTimeEnd">
        <text class="clear-btn" @tap="clearEndTimeRange">清除</text>
      </view>
    </view>

    <!-- 日期时间选择器组件 -->
    <!-- 开始时间-开始 -->
    <CustomDateTimePicker
      v-model="showStartTimePicker"
      v-model:value="startTimeStart"
      title="选择开始时间"
      format="yyyy-MM-dd HH:mm:ss"
      :show-time="true"
      :show-seconds="false"
      @change="handleStartTimeStartChange"
    />

    <!-- 开始时间-结束 -->
    <CustomDateTimePicker
      v-model="showEndTimePicker"
      v-model:value="startTimeEnd"
      title="选择结束时间"
      format="yyyy-MM-dd HH:mm:ss"
      :show-time="true"
      :show-seconds="false"
      @change="handleStartTimeEndChange"
    />

    <!-- 结束时间-开始 -->
    <CustomDateTimePicker
      v-model="showEndTimeStartPicker"
      v-model:value="endTimeStart"
      title="选择结束时间"
      format="yyyy-MM-dd HH:mm:ss"
      :show-time="true"
      :show-seconds="false"
      @change="handleEndTimeStartChange"
    />

    <!-- 结束时间-结束 -->
    <CustomDateTimePicker
      v-model="showEndTimeEndPicker"
      v-model:value="endTimeEnd"
      title="选择结束时间"
      format="yyyy-MM-dd HH:mm:ss"
      :show-time="true"
      :show-seconds="false"
      @change="handleEndTimeEndChange"
    />
  </CustomPopup>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
import CustomDateTimePicker from '@/components/CustomDateTimePicker/CustomDateTimePicker.vue'

// 获取当前实例上下文
const { proxy } = getCurrentInstance()

const props = defineProps({
  // 控制弹窗显示状态
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 初始筛选参数
  defaultParams: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 弹窗可见性
const visible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

// 更新弹窗可见性
const updateVisible = val => {
  visible.value = val
}

// 日期时间选择器显示状态
const showStartTimePicker = ref(false)
const showEndTimePicker = ref(false)
const showEndTimeStartPicker = ref(false)
const showEndTimeEndPicker = ref(false)

// 日期时间值
const startTimeStart = ref(new Date())
const startTimeEnd = ref(new Date())
const endTimeStart = ref(new Date())
const endTimeEnd = ref(new Date())

// 筛选参数
const filterParams = reactive({
  orderCode: '', // 采购单号
  startTimeStart: '', // 开始时间-开始
  startTimeEnd: '', // 开始时间-结束
  endTimeStart: '', // 结束时间-开始
  endTimeEnd: '', // 结束时间-结束
})

// 格式化日期时间显示
const formatDateTime = (date, format = 'yyyy-MM-dd HH:mm') => {
  if (!date || isNaN(new Date(date).getTime())) return ''

  const dateObj = new Date(date)
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hour = String(dateObj.getHours()).padStart(2, '0')
  const minute = String(dateObj.getMinutes()).padStart(2, '0')
  const second = String(dateObj.getSeconds()).padStart(2, '0')

  return format
    .replace(/yyyy/g, year)
    .replace(/MM/g, month)
    .replace(/dd/g, day)
    .replace(/HH/g, hour)
    .replace(/mm/g, minute)
    .replace(/ss/g, second)
}

// 格式化显示的日期时间
const formattedStartTime = computed(() => {
  return filterParams.startTimeStart ? formatDateTime(filterParams.startTimeStart) : ''
})

const formattedEndTime = computed(() => {
  return filterParams.startTimeEnd ? formatDateTime(filterParams.startTimeEnd) : ''
})

const formattedEndTimeStart = computed(() => {
  return filterParams.endTimeStart ? formatDateTime(filterParams.endTimeStart) : ''
})

const formattedEndTimeEnd = computed(() => {
  return filterParams.endTimeEnd ? formatDateTime(filterParams.endTimeEnd) : ''
})

// 处理开始时间-开始变化
const handleStartTimeStartChange = date => {
  filterParams.startTimeStart = formatDateTime(date, 'yyyy-MM-dd HH:mm:ss')
}

// 处理开始时间-结束变化
const handleStartTimeEndChange = date => {
  filterParams.startTimeEnd = formatDateTime(date, 'yyyy-MM-dd HH:mm:ss')
}

// 处理结束时间-开始变化
const handleEndTimeStartChange = date => {
  filterParams.endTimeStart = formatDateTime(date, 'yyyy-MM-dd HH:mm:ss')
}

// 处理结束时间-结束变化
const handleEndTimeEndChange = date => {
  filterParams.endTimeEnd = formatDateTime(date, 'yyyy-MM-dd HH:mm:ss')
}

// 清除开始时间范围
const clearStartTimeRange = () => {
  filterParams.startTimeStart = ''
  filterParams.startTimeEnd = ''
}

// 清除结束时间范围
const clearEndTimeRange = () => {
  filterParams.endTimeStart = ''
  filterParams.endTimeEnd = ''
}

// 设置类型
const setType = type => {
  if (filterParams.type === type) {
    filterParams.type = ''
  } else {
    filterParams.type = type
  }
}

// 确认筛选
const handleConfirm = () => {
  emit('confirm', { ...filterParams })
}

// 重置筛选
const handleReset = () => {
  // 重置所有筛选参数
  filterParams.orderCode = ''
  filterParams.startTimeStart = ''
  filterParams.startTimeEnd = ''
  filterParams.endTimeStart = ''
  filterParams.endTimeEnd = ''

  // 重置日期时间选择器的值
  startTimeStart.value = new Date()
  startTimeEnd.value = new Date()
  endTimeStart.value = new Date()
  endTimeEnd.value = new Date()
}

// 初始化数据
const initData = () => {
  if (props.defaultParams) {
    // 合并默认参数
    Object.assign(filterParams, props.defaultParams)

    // 初始化日期时间值
    if (filterParams.startTimeStart) {
      startTimeStart.value = new Date(filterParams.startTimeStart)
    }
    if (filterParams.startTimeEnd) {
      startTimeEnd.value = new Date(filterParams.startTimeEnd)
    }
    if (filterParams.endTimeStart) {
      endTimeStart.value = new Date(filterParams.endTimeStart)
    }
    if (filterParams.endTimeEnd) {
      endTimeEnd.value = new Date(filterParams.endTimeEnd)
    }
  }
}

// 初始化
initData()
</script>

<style lang="scss" scoped>
.filter-item {
  margin-bottom: 30rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-input-container {
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
}

.filter-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}

.filter-types {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.type-item {
  margin: 10rpx;
  padding: 0 30rpx;
  height: 70rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;

  &.active {
    border-color: #2979ff;
    background-color: rgba(41, 121, 255, 0.1);
    color: #2979ff;
  }
}

.time-range {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.separator {
  margin: 0 20rpx;
  color: #999;
  font-size: 28rpx;
}

.datetime-picker {
  flex: 1;
}

.datetime-text {
  font-size: 28rpx;
  color: #333;
}

.clear-btn-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
}

.clear-btn {
  font-size: 26rpx;
  color: #2979ff;
  padding: 10rpx 20rpx;
}
</style>
