<template>
  <view class="device-detail-container">
    <!-- 顶部Tab导航 -->
    <view class="device-detail-header">
      <CustomTab
        v-model="currentTab"
        :tabs="tabList"
        style-type="text"
        active-color="#2979FF"
        in-active-color="#999"
        @change="handleTabChange"
      />
    </view>

    <!-- Tab内容区域 - 使用v-if控制 -->
    <view class="tab-content-container">
      <!-- 基本信息页面 -->
      <view v-if="currentTab === 0" class="basic-info-content">
        <BasicInfoPanel :device-data="deviceInfo" />
      </view>

      <!-- 维保记录页面 -->
      <view v-if="currentTab === 1" class="maintenance-records-content">
        <MaintenanceRecordsPanel :records="maintenanceRecords" :sort-type="sortType" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomTab from '@/components/CustomTab/CustomTab.vue'
import BasicInfoPanel from './components/basicInfoPanel.vue'
import MaintenanceRecordsPanel from './components/maintenanceRecordsPanel.vue'
import { deviceDetail, maintenanceRecord } from '@/api/device'
const { proxy } = getCurrentInstance()

/**
 * 设备详情页面
 * @description 显示设备的基本信息和维保记录
 */

// Tab选项配置
const tabList = ref([
  { name: '基本信息', id: 'basic' },
  { name: '维保记录', id: 'maintenance' },
])

// 当前选中的Tab
const currentTab = ref(0)

// 设备基本信息
const deviceInfo = ref({
  id: '',
  deviceName: '', // 设备名称
  code: '', // 设备编号
  categoryName: '', // 设备分类
  deviceImg: '', // 设备图片
  specifications: '', // 设备规格
  deviceState: 0, // 设备状态 0:离线 1:在线 3:故障
  isolateState: 0, // 隔离状态 0:隔离 1:未隔离
  model: '', // 设备型号
  sign: '', // 设备标识
  pointName: '', // 点位名称
  pointId: '', // 点位ID
  certificateNo: '', // 证书编号
  certificate: '', // 证书图片
  remark: '', // 备注
})

// 维保记录列表
const maintenanceRecords = ref([])

// 排序类型
const sortType = ref(0)

/**
 * 处理Tab切换事件
 * @param {Object} e - 切换事件对象
 */
const handleTabChange = e => {
  // 如果切换到维保记录页面，可以在这里加载数据
  if (e.currentIndex === 1 && maintenanceRecords.value.length === 0) {
    loadMaintenanceRecords()
  }
}

/**
 * 获取设备详细信息
 * @param {String} deviceId - 设备ID
 */
const loadDeviceDetail = async deviceId => {
  try {
    proxy.$modal.loading('加载中...')
    // TODO: 调用API获取设备详细信息
    const res = await deviceDetail({ id: deviceId })
    deviceInfo.value = res.data
    proxy.$modal.closeLoading()
  } catch (error) {
    proxy.$modal.closeLoading()
    proxy.$modal.showToast('获取设备详情失败')
  }
}

/**
 * 加载维保记录
 */
const loadMaintenanceRecords = async deviceId => {
  try {
    proxy.$modal.loading('加载中...')
    const res = await maintenanceRecord({ deviceId: deviceId })
    maintenanceRecords.value = res.data
    proxy.$modal.closeLoading()
  } catch (error) {
    proxy.$modal.closeLoading()
    proxy.$modal.showToast('获取维保记录失败')
  }
}

// 页面加载时的初始化 - 移除uni.getCurrentPages的使用
onLoad(({ deviceId, type = 0 }) => {
  sortType.value = parseInt(type) || 0

  // 获取设备信息
  loadDeviceDetail(deviceId)

  // 设置排序类型

  // 初始化加载维保记录
  loadMaintenanceRecords(deviceId)
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.device-detail-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: $uni-height-area;
  background-color: #f5f5f5;
}

.device-detail-header {
  flex-shrink: 0;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tab-content-container {
  flex: 1;
  flex-shrink: 0;
  min-height: 0;
  overflow-y: scroll;
  background-color: #f5f5f5;
}

.basic-info-content,
.maintenance-records-content {
  width: 100%;
  min-height: 100%;
  padding: 32rpx;
}
</style>
