import request from "@/utils/request";

// 获取库存列表
export function getInventoryList(params) {
  return request({
    url: '/stockList',
    method: 'post',
    data: params,
  })
}

// 获取库存详情
export function getInventoryDetail(params) {
  return request({
    url: '/stockInfo',
    method: 'get',
    params,
  })
}

// 库存明细
export function getInventoryDetailList(params) {
  return request({
    url: '/stockDeviceInfo',
    method: 'get',
    params,
  })
}

// 库存记录
export function getInventoryRecordList(params) {
  return request({
    url: '/stockRecordInfo',
    method: 'get',
    params,
  })
}


// 库存操作（调整、出库、入库）
export function updateInventoryOperation(params) {
  return request({
    url: '/stockAdjustment',
    method: 'post',
    data: params,
  })
}

// 获取所有未处理设备维保订单
export function getUnprocessedDevices(params) {
  return request({
    url: '/getMaintainOrderDevice',
    method: 'get',
    params,
  })
}
