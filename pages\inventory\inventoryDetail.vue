<template>
  <view class="inventory-detail">
    <!-- 顶部Tab导航 -->
    <CustomTab :tabs="tabList" v-model="currentTab" style-type="text" @change="handleTabChange" />

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- Tab1: 基本信息 -->
      <view v-if="currentTab === 0" class="basic-info-tab">
        <BasicInfo :detail="inventoryDetail" :loading="loading" />
      </view>

      <!-- Tab2: 库存明细 -->
      <view v-if="currentTab === 1" class="detail-tab">
        <InventoryDetailTable :data="detailList" :loading="detailLoading" />
      </view>

      <!-- Tab3: 库存记录 -->
      <view v-if="currentTab === 2" class="record-tab">
        <InventoryRecordTable :data="recordList" :loading="recordLoading" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'InventoryDetail',
}
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomTab from '@/components/CustomTab/CustomTab.vue'
import BasicInfo from './components/BasicInfo.vue'
import InventoryDetailTable from './components/InventoryDetailTable.vue'
import InventoryRecordTable from './components/InventoryRecordTable.vue'
import { getInventoryDetail, getInventoryDetailList, getInventoryRecordList } from '@/api/inventory'

const { proxy } = getCurrentInstance()

// Tab配置
const tabList = ref(['基本信息', '库存明细', '库存记录'])

// 当前选中的Tab
const currentTab = ref(0)

// 页面参数
const inventoryId = ref('')

// 基本信息数据
const inventoryDetail = ref({})
const loading = ref(false)

// 库存明细数据
const detailList = ref([])
const detailLoading = ref(false)

// 库存记录数据
const recordList = ref([])
const recordLoading = ref(false)

// Tab切换处理
const handleTabChange = changeInfo => {
  const index = changeInfo.currentIndex
  currentTab.value = index

  // 根据切换的Tab加载对应数据
  switch (index) {
    case 0:
      if (!inventoryDetail.value.id) {
        loadBasicInfo()
      }
      break
    case 1:
      loadDetailList()
      break
    case 2:
      loadRecordList()
      break
  }
}

// 加载基本信息
const loadBasicInfo = async () => {
  if (!inventoryId.value) return

  try {
    loading.value = true
    const res = await getInventoryDetail({ id: inventoryId.value })
    inventoryDetail.value = res.data || {}
  } catch (error) {
    proxy.$modal.msgError('获取基本信息失败')
    console.error('获取基本信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载库存明细
const loadDetailList = async () => {
  if (!inventoryId.value) return

  try {
    detailLoading.value = true
    const res = await getInventoryDetailList({ id: inventoryId.value })
    console.log('库存明细API响应:', res)
    detailList.value = res.data || []
    console.log('库存明细数据:', detailList.value)
  } catch (error) {
    proxy.$modal.msgError('获取库存明细失败')
    console.error('获取库存明细失败:', error)
    detailList.value = []
  } finally {
    detailLoading.value = false
  }
}

// 加载库存记录
const loadRecordList = async () => {
  if (!inventoryId.value) return

  try {
    recordLoading.value = true
    const res = await getInventoryRecordList({ id: inventoryId.value })
    console.log('库存记录API响应:', res)
    recordList.value = res.data || []
    console.log('库存记录数据:', recordList.value)
  } catch (error) {
    proxy.$modal.msgError('获取库存记录失败')
    console.error('获取库存记录失败:', error)
    recordList.value = []
  } finally {
    recordLoading.value = false
  }
}

// 使用 onLoad 获取页面参数
onLoad(option => {
  inventoryId.value = option.id || ''
  if (inventoryId.value) {
    // 默认加载基本信息
    loadBasicInfo()
  } else {
    proxy.$modal.msgError('缺少库存ID参数')
  }
})
</script>

<style lang="scss" scoped>
.inventory-detail {
  height: $uni-height-area;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.content-container {
  padding: 20rpx;
  min-height: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.basic-info-tab,
.detail-tab,
.record-tab {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  flex: 1;
  display: flex;
  min-height: 0;
  flex-direction: column;
}

.detail-tab,
.record-tab {
  // 表格Tab需要占满剩余空间
  height: 100%;
}
</style>
