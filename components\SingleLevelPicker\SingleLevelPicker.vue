<template>
  <uni-popup ref="popup" type="bottom" @change="handlePopupChange">
    <view class="picker-container">
      <!-- 头部 -->
      <view class="picker-header">
        <text class="cancel-btn" @tap="handleCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm-btn" @tap="handleConfirm">确定</text>
      </view>

      <!-- 选项列表 -->
      <view class="picker-body">
        <picker-view class="picker-view" :value="currentValue" @change="handlePickerChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in options" :key="index">
              {{ getItemText(item) }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  // 控制显示
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 选项列表
  options: {
    type: Array,
    default: () => [],
  },
  // 标题
  title: {
    type: String,
    default: '请选择',
  },
  // 显示文本的字段名
  textField: {
    type: String,
    default: 'label',
  },
  // 绑定ID的字段名
  valueField: {
    type: String,
    default: 'value',
  },
  // 当前选中的值
  value: {
    type: [String, Number],
    default: null,
  },
  // 最大高度
  maxHeight: {
    type: Number,
    default: 600,
  },
})

const emit = defineEmits(['update:modelValue', 'update:value', 'select', 'cancel', 'confirm'])

// popup 引用
const popup = ref(null)

// 当前选中的索引值
const currentValue = ref([0])

// 获取选项显示的文本
const getItemText = item => {
  if (typeof item === 'string') {
    return item
  }
  return item[props.textField] || ''
}

// 获取选项的值
const getItemValue = item => {
  if (typeof item === 'string') {
    return item
  }
  return item[props.valueField]
}

// 监听显示状态
watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      // 打开时，根据当前value设置选中值
      const currentIndex = props.options.findIndex(item => getItemValue(item) === props.value)
      currentValue.value = [Math.max(0, currentIndex)]
      popup.value?.open('bottom')
    } else {
      popup.value?.close()
    }
  }
)

// 监听value变化，更新currentValue
watch(
  () => props.value,
  newVal => {
    if (newVal !== undefined && props.options.length > 0) {
      const currentIndex = props.options.findIndex(item => getItemValue(item) === newVal)
      currentValue.value = [Math.max(0, currentIndex)]
    }
  },
  { immediate: true }
)

// 处理选择器变化
const handlePickerChange = e => {
  // 只更新内部状态，不触发任何数据更新事件
  currentValue.value = e.detail.value
}

// 处理弹窗状态变化
const handlePopupChange = e => {
  // 当弹窗关闭时，同步更新 modelValue
  if (e.show === false) {
    emit('update:modelValue', false)
  }
}

// 处理取消
const handleCancel = () => {
  // 重置为原来的值，不触发任何数据更新事件
  const currentIndex = props.options.findIndex(item => getItemValue(item) === props.value)
  currentValue.value = [Math.max(0, currentIndex)]
  popup.value?.close()
  emit('cancel')
  emit('update:modelValue', false)
}

// 处理确定
const handleConfirm = () => {
  const selectedIndex = currentValue.value[0]
  const selectedItem = props.options[selectedIndex]

  if (selectedItem) {
    const itemValue = getItemValue(selectedItem)
    // 只在确定时才触发所有数据更新事件
    emit('update:value', itemValue)
    emit('select', selectedItem, itemValue)
    emit('confirm', selectedItem, itemValue)
  }
  popup.value?.close()
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.picker-container {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.cancel-btn {
  font-size: 32rpx;
  color: #999;
}

.confirm-btn {
  font-size: 32rpx;
  color: #2979ff;
}

.picker-body {
  height: 500rpx;
}

.picker-view {
  width: 100%;
  height: 100%;
}

.picker-item {
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}
</style>
