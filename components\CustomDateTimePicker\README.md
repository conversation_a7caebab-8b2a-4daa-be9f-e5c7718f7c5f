# CustomDateTimePicker 日期时间选择器组件

一个功能完整的Vue日期时间选择器组件，支持年、月、日、时、分、秒的完整选择，并提供灵活的格式化配置。

## 功能特性

- ✅ 支持完整的日期时间选择（年、月、日、时、分、秒）
- ✅ 灵活的显示配置（可选择是否显示时间、是否显示秒）
- ✅ 自定义格式化格式
- ✅ 智能日期校验（自动处理月份天数变化）
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 基于uni-popup的底部弹出式交互
- ✅ 与现有CustomTimePicker组件保持一致的设计风格

## 组件Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | Boolean | false | 控制选择器显示/隐藏状态 |
| value | Date/Number/String | new Date() | 当前选中的日期时间值 |
| title | String | '选择日期时间' | 选择器标题 |
| format | String | 'yyyy-MM-dd HH:mm:ss' | 日期时间格式化格式 |
| minYear | Number | 1970 | 最小年份 |
| maxYear | Number | 当前年份+10 | 最大年份 |
| showTime | Boolean | true | 是否显示时间选择（时、分、秒） |
| showSeconds | Boolean | true | 是否显示秒选择 |

## 组件Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | Boolean | 更新显示状态 |
| update:value | Date | 更新选中的日期时间值 |
| change | Date | 选择器值变化时触发（实时触发） |

## 格式化格式说明

支持以下格式化占位符：

- `yyyy` - 四位年份（如：2024）
- `MM` - 两位月份（如：01-12）
- `dd` - 两位日期（如：01-31）
- `HH` - 两位小时（如：00-23）
- `mm` - 两位分钟（如：00-59）
- `ss` - 两位秒钟（如：00-59）

### 常用格式示例

- `yyyy-MM-dd HH:mm:ss` → 2024-03-15 14:30:25
- `yyyy-MM-dd HH:mm` → 2024-03-15 14:30
- `yyyy-MM-dd` → 2024-03-15
- `yyyy/MM/dd HH:mm:ss` → 2024/03/15 14:30:25
- `MM-dd HH:mm:ss` → 03-15 14:30:25

## 基本使用

### 1. 在页面中引入组件

```vue
<template>
  <view>
    <!-- 触发按钮 -->
    <view @tap="showDatePicker = true">
      <text>{{ formattedDate || '请选择日期时间' }}</text>
    </view>
    
    <!-- 日期时间选择器 -->
    <CustomDateTimePicker
      v-model="showDatePicker"
      v-model:value="selectedDate"
      title="选择日期时间"
      format="yyyy-MM-dd HH:mm:ss"
      @change="handleDateChange"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import CustomDateTimePicker from '@/components/CustomDateTimePicker/CustomDateTimePicker.vue'

const showDatePicker = ref(false)
const selectedDate = ref(new Date())

// 格式化显示
const formattedDate = computed(() => {
  return formatDate(selectedDate.value, 'yyyy-MM-dd HH:mm:ss')
})

function handleDateChange(date) {
  console.log('选择的日期时间：', date)
}

function formatDate(date, format) {
  // 格式化函数实现...
}
</script>
```

### 2. 不同配置示例

#### 只选择日期（不显示时间）

```vue
<CustomDateTimePicker
  v-model="showDatePicker"
  v-model:value="selectedDate"
  title="选择日期"
  format="yyyy-MM-dd"
  :show-time="false"
/>
```

#### 选择日期和时分（不显示秒）

```vue
<CustomDateTimePicker
  v-model="showDatePicker"
  v-model:value="selectedDate"
  title="选择日期时间"
  format="yyyy-MM-dd HH:mm"
  :show-time="true"
  :show-seconds="false"
/>
```

#### 自定义年份范围

```vue
<CustomDateTimePicker
  v-model="showDatePicker"
  v-model:value="selectedDate"
  title="选择日期时间"
  :min-year="2020"
  :max-year="2030"
/>
```

## 暴露的方法

组件通过 `defineExpose` 暴露了以下方法：

- `formatDate(date, format)` - 格式化日期方法

```vue
<script setup>
import { ref } from 'vue'

const datePickerRef = ref(null)

// 使用暴露的方法
function formatCurrentDate() {
  const formatted = datePickerRef.value.formatDate(new Date(), 'yyyy-MM-dd')
  console.log(formatted)
}
</script>

<template>
  <CustomDateTimePicker ref="datePickerRef" />
</template>
```

## 注意事项

1. **自动注册**: 由于项目配置了 `easycom`，组件会自动注册，无需手动导入
2. **日期校验**: 组件会自动处理月份天数变化，确保选择的日期有效
3. **响应式**: 组件支持响应式数据绑定，值变化会自动更新显示
4. **兼容性**: 基于uni-app框架，支持多端运行

## 完整示例

参考 `example.vue` 文件查看完整的使用示例，包含多种配置场景的演示。
