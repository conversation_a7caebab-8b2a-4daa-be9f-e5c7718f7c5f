<template>
  <view>
    <!-- 一级设备类型选择器 -->
    <CustomActionSheet
      v-model="showFirstDeviceType"
      :options="needAccessory ? device_category : device_type"
      :title="firstLevelTitle"
      @select="handleFirstDeviceTypeSelect"
    />

    <!-- 二级设备类型选择器 -->
    <CustomActionSheet
      v-model="showSecondDeviceType"
      :options="secondDeviceTypeList"
      :title="secondLevelTitle"
      @select="handleSecondDeviceTypeSelect"
    />
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance, watch } from 'vue'
import CustomActionSheet from '@/components/CustomActionSheet/CustomActionSheet.vue'
import { sonDeviceType } from '@/api/device'

// 获取当前实例上下文
const { proxy } = getCurrentInstance()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  selectedDeviceType: {
    type: [String, Number],
    default: '',
  },
  firstLevelTitle: {
    type: String,
    default: '选择设备类型',
  },
  secondLevelTitle: {
    type: String,
    default: '选择设备子类型',
  },
  // 是否需要 配件和其他
  needAccessory: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'update:selectedDeviceType', 'select'])

const dict_string = props.needAccessory ? 'device_category' : 'device_type'

// 设备类型字典
const { device_category, device_type } = proxy.useTypeList(dict_string)

// 选择器状态
const showFirstDeviceType = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})
const showSecondDeviceType = ref(false)

// 设备类型数据
const secondDeviceTypeList = ref([])

// 设备类型信息
const deviceTypeInfo = ref({
  firstLevelId: '', // 一级设备类型ID
  firstLevelName: '', // 一级设备类型名称
  secondLevelId: '', // 二级设备类型ID
  secondLevelName: '', // 二级设备类型名称
  fullName: '', // 完整设备类型名称（一级+二级）
})

// 监听选中的设备类型变化
watch(
  () => props.selectedDeviceType,
  function (newVal) {
    if (newVal) {
      // 这里可以添加根据ID查询设备类型信息的逻辑
    } else {
      resetDeviceTypeInfo()
    }
  },
  { immediate: true }
)

// 处理一级设备类型选择
function handleFirstDeviceTypeSelect(item, value) {
  deviceTypeInfo.value.firstLevelId = value
  deviceTypeInfo.value.firstLevelName = item.label

  // 清空二级设备类型
  deviceTypeInfo.value.secondLevelId = ''
  deviceTypeInfo.value.secondLevelName = ''
  deviceTypeInfo.value.fullName = ''

  // 获取二级设备类型
  getSecondDeviceTypes(value)
}

// 处理二级设备类型选择
function handleSecondDeviceTypeSelect(item, value) {
  deviceTypeInfo.value.secondLevelId = value
  deviceTypeInfo.value.secondLevelName = item.label
  deviceTypeInfo.value.fullName = `${deviceTypeInfo.value.firstLevelName} - ${item.label}`

  // 更新选中的设备类型
  emit('update:selectedDeviceType', value)

  // 触发选择完成事件
  emit('select', {
    id: value,
    name: item.label,
    firstLevelId: deviceTypeInfo.value.firstLevelId,
    firstLevelName: deviceTypeInfo.value.firstLevelName,
    secondLevelId: value,
    secondLevelName: item.label,
    fullName: deviceTypeInfo.value.fullName,
  })

  // 关闭选择器
  showFirstDeviceType.value = false
}

// 获取二级设备类型
async function getSecondDeviceTypes(parentId) {
  try {
    proxy.$modal.loading('加载中...')
    const res = await sonDeviceType({ fatherId: parentId })
    secondDeviceTypeList.value = res.data.map(item => ({
      label: item.dictLabel,
      value: item.dictValue,
    }))

    // 显示二级设备类型选择器
    showSecondDeviceType.value = true
  } catch (error) {
    proxy.$modal.msgError('获取设备子类型失败')
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 重置设备类型信息
function resetDeviceTypeInfo() {
  deviceTypeInfo.value = {
    firstLevelId: '',
    firstLevelName: '',
    secondLevelId: '',
    secondLevelName: '',
    fullName: '',
  }
}

// 对外暴露方法
defineExpose({
  resetDeviceTypeInfo,
})
</script>

<style lang="scss" scoped>
/* 无需额外样式 */
</style>
