<!-- 设备选择弹窗 -->
<template>
  <CustomPopup v-model="showPopup" :title="title" reset-text="重置" @reset="resetForm" @confirm="confirmSelect">
    <view class="device-selector-container">
      <!-- 类型为移除时只需要选择点位 -->
      <template v-if="type === 'remove'">
        <view class="form-item">
          <view class="form-label required">
            <text>具体位置</text>
          </view>
          <view class="form-value" @tap="openLocationPicker">
            <text v-if="selectedLocation" class="form-text">{{ selectedLocation }}</text>
            <text v-else class="form-placeholder">请选择</text>
            <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
          </view>
        </view>
      </template>

      <!-- 类型为新增或修改时 -->
      <template v-else>
        <!-- 具体位置 -->
        <view class="form-item">
          <view class="form-label required">
            <text>具体位置</text>
          </view>
          <view class="form-value" @tap="openLocationPicker">
            <text v-if="selectedLocation" class="form-text">{{ selectedLocation }}</text>
            <text v-else class="form-placeholder">请选择</text>
            <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
          </view>
        </view>

        <!-- 设备类型 - 只有选择了点位后才显示 -->
        <view v-if="selectedLocation" class="form-item">
          <view class="form-label required">
            <text>设备类型</text>
          </view>
          <view class="form-value" @tap="openDeviceTypePicker">
            <text v-if="selectedDeviceTypeName" class="form-text">{{ selectedDeviceTypeName }}</text>
            <text v-else class="form-placeholder">请选择</text>
            <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
          </view>
        </view>

        <!-- 设备名称 - 只有选择了设备类型后才显示 -->
        <view v-if="selectedDeviceType" class="form-item">
          <view class="form-label required">
            <text>设备名称</text>
          </view>
          <view class="form-value" @tap="openDeviceNamePicker">
            <text v-if="selectedDevice" class="form-text">{{ selectedDeviceName }}</text>
            <text v-else class="form-placeholder">请选择</text>
            <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
          </view>
        </view>

        <!-- 设备信息展示区域 - 只有选择了设备名称后才显示 -->
        <view v-if="deviceInfo" class="device-info-container">
          <!-- 设备型号 -->
          <view class="form-item">
            <view class="form-label">
              <text>设备型号</text>
            </view>
            <view class="form-value">
              <text class="form-text">{{ deviceInfo.model || '--' }}</text>
            </view>
          </view>

          <!-- 设备规格 -->
          <view class="form-item">
            <view class="form-label">
              <text>设备规格</text>
            </view>
            <view class="form-value">
              <text class="form-text">{{ deviceInfo.specifications || '--' }}</text>
            </view>
          </view>

          <!-- 设备图片 -->
          <view class="form-item">
            <view class="form-label">
              <text>设备图片</text>
            </view>
            <view class="form-value">
              <text v-if="deviceInfo.deviceImg" class="form-text link-text" @tap="viewDeviceImage">查看</text>
              <text v-else class="form-text">暂无图片</text>
            </view>
          </view>

          <!-- 库存数量 -->
          <view class="form-item">
            <view class="form-label">
              <text>库存数量</text>
            </view>
            <view class="form-value">
              <text class="form-text">{{ deviceInfo.number }}</text>
            </view>
          </view>

          <!-- 本次使用数量 -->
          <view class="form-item">
            <view class="form-label">
              <text>本次使用数量</text>
            </view>
            <view class="form-value">
              <!-- <uni-number-box v-model="useCount" :min="1" :max="deviceInfo.stock || 99" /> -->
              <text class="form-text">{{ useCount }}</text>
            </view>
          </view>
        </view>

        <view v-if="!deviceInfo && selectedDevice" class="loading-container">
          <uni-icons type="spinner-cycle" size="30" color="#2979ff"></uni-icons>
          <text class="loading-text">加载设备信息中...</text>
        </view>
      </template>

      <view v-if="error" class="error-message">
        <text>{{ error }}</text>
      </view>
    </view>
  </CustomPopup>

  <!-- 位置选择器 -->
  <LocationPickerPopup
    v-model="showLocationPicker"
    v-model:selected-value="locationId"
    :is-filter="true"
    :filter-function="filterFunction"
    @confirm="handleLocationConfirm"
  />

  <!-- 设备类型选择器 -->
  <DeviceTypePicker
    v-model="showDeviceTypePicker"
    v-model:selectedDeviceType="selectedDeviceType"
    @select="handleDeviceTypeSelect"
  />

  <!-- 设备名称选择器 -->
  <CustomActionSheet
    v-model="showDeviceNamePicker"
    v-model:value="selectedDevice"
    :options="deviceNameOptions"
    text-field="deviceName"
    value-field="id"
    title="设备名称"
    @select="handleDeviceNameSelect"
  />

  <!-- 设备图片查看器 -->
  <CustomPopup v-model="showImageViewer" title="设备图片" :show-footer="false">
    <view class="image-viewer">
      <image
        v-if="deviceInfo && deviceInfo.deviceImg"
        :src="deviceInfo.deviceImg"
        mode="aspectFit"
        class="device-image"
      ></image>
      <view v-else class="no-image">
        <text>暂无图片</text>
      </view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
import CustomActionSheet from '@/components/CustomActionSheet/CustomActionSheet.vue'
import LocationPickerPopup from '@/components/LocationPickerPopup/LocationPickerPopup.vue'
import DeviceTypePicker from '@/components/DeviceTypePicker/DeviceTypePicker.vue'
import { getDeviceNameList } from '@/api/device'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'add', // 'add', 'edit', 'remove'
    validator: value => ['add', 'edit', 'remove'].includes(value),
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 获取当前实例上下文
const { proxy } = getCurrentInstance()

// 弹窗显示状态
const showPopup = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

// 标题
const title = computed(() => {
  const typeMap = {
    add: '添加设备',
    edit: '更换设备',
    remove: '移除设备',
  }
  return typeMap[props.type] || '选择设备'
})

// 选择器状态
const showLocationPicker = ref(false)
const showDeviceTypePicker = ref(false)
const showDeviceNamePicker = ref(false)
const showImageViewer = ref(false)

// 选中的值
const locationId = ref('') // 位置ID
const selectedLocation = ref('') // 位置名称
const selectedDeviceType = ref('') // 设备类型ID
const selectedDeviceTypeName = ref('') // 设备类型名称
const selectedDevice = ref('') // 设备ID
const selectedDeviceName = ref('') // 设备名称
const useCount = ref(1)

// 错误信息
const error = ref('')

// 设备信息
const deviceInfo = ref(null)

// 设备名称选项
const deviceNameOptions = ref([])

const filterFunction = item => {
  if (props.type === 'add') {
    return item.used
  } else {
    return !item.used
  }
}

// 打开位置选择器
function openLocationPicker() {
  showLocationPicker.value = true
}

// 处理位置选择确认
function handleLocationConfirm(location) {
  locationId.value = location.pointId
  selectedLocation.value = location.pointName

  // 清空设备类型和设备名称
  resetDeviceSelection()
}

// 打开设备类型选择器
function openDeviceTypePicker() {
  if (!selectedLocation.value) {
    proxy.$modal.msg('请先选择具体位置')
    return
  }
  showDeviceTypePicker.value = true
}

// 处理设备类型选择
function handleDeviceTypeSelect(deviceType) {
  selectedDeviceType.value = deviceType.id
  selectedDeviceTypeName.value = deviceType.fullName

  // 清空设备名称
  selectedDevice.value = ''
  selectedDeviceName.value = ''
  deviceInfo.value = null

  // 获取设备名称列表
  getDeviceNameOptions()
}

// 获取设备名称选项
async function getDeviceNameOptions() {
  try {
    proxy.$modal.loading('加载中...')
    const res = await getDeviceNameList({ categoryId: selectedDeviceType.value })
    deviceNameOptions.value = res.data
  } catch (error) {
    proxy.$modal.msgError('获取设备名称失败')
    deviceNameOptions.value = []
  } finally {
    proxy.$modal.closeLoading()
  }
}

// 打开设备名称选择器
function openDeviceNamePicker() {
  if (!selectedDeviceType.value) {
    proxy.$modal.msg('请先选择设备类型')
    return
  }

  if (deviceNameOptions.value.length === 0) {
    proxy.$modal.msg('暂无可选设备')
    return
  }

  showDeviceNamePicker.value = true
}

function handleDeviceNameSelect(item) {
  selectedDevice.value = item.id
  selectedDeviceName.value = item.deviceName
  deviceInfo.value = item
}

// 查看设备图片
function viewDeviceImage() {
  if (deviceInfo.value && deviceInfo.value.deviceImg) {
    showImageViewer.value = true
  } else {
    proxy.$modal.msg('暂无设备图片')
  }
}

// 确认选择
function confirmSelect() {
  if (props.type === 'remove') {
    if (!selectedLocation.value) {
      proxy.$modal.msg('请选择具体位置')
      return
    }

    emit('confirm', {
      deviceId: selectedDevice.value,
      deviceName: selectedDeviceName.value,
      pointId: locationId.value,
      useNum: useCount.value,
    })
  } else {
    if (!selectedLocation.value) {
      proxy.$modal.msg('请选择具体位置')
      return
    }

    if (!selectedDeviceType.value) {
      proxy.$modal.msg('请选择设备类型')
      return
    }

    if (!selectedDevice.value) {
      proxy.$modal.msg('请选择设备名称')
      return
    }

    if (!deviceInfo.value) {
      proxy.$modal.msg('设备信息加载中，请稍后')
      return
    }

    emit('confirm', {
      deviceId: selectedDevice.value,
      deviceName: selectedDeviceName.value,
      pointId: locationId.value,
      useNum: useCount.value,
    })
  }

  // 关闭弹窗
  showPopup.value = false
}

// 重置设备选择
function resetDeviceSelection() {
  selectedDeviceType.value = ''
  selectedDeviceTypeName.value = ''
  selectedDevice.value = ''
  selectedDeviceName.value = ''
  deviceInfo.value = null
  deviceNameOptions.value = []
}

// 重置表单
function resetForm() {
  locationId.value = ''
  selectedLocation.value = ''
  resetDeviceSelection()
  error.value = ''
}

// 监听弹窗显示状态变化
watch(showPopup, newVal => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.device-selector-container {
  padding: 20rpx 0;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.required::before {
  content: '*';
  color: #ff0000;
  margin-right: 4rpx;
}

.form-value {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.form-text {
  font-size: 32rpx;
  color: #333;
  margin-right: 10rpx;
}

.form-placeholder {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}

.link-text {
  color: #2979ff;
}

.device-info-container {
  margin-top: 20rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

.error-message {
  padding: 20rpx 30rpx;
  color: #ff0000;
  font-size: 28rpx;
}

.image-viewer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 500rpx;
}

.device-image {
  max-width: 100%;
  max-height: 100%;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
  font-size: 28rpx;
}
</style>
