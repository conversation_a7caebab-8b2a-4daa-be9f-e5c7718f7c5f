<template>
  <uni-popup ref="popup" type="bottom" @change="handlePopupChange">
    <view class="picker-container">
      <view class="picker-header">
        <text class="cancel-btn" @tap="handleCancel">取消</text>
        <text class="title">{{ title }}</text>
        <text class="confirm-btn" @tap="handleConfirm">确定</text>
      </view>
      <view class="picker-body">
        <picker-view class="picker-view" :value="currentValue" @change="handlePickerChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in hoursArray" :key="index"> {{ item }}小时 </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in minutesArray" :key="index"> {{ item }}分钟 </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'CustomTimePicker',
}
</script>

<script setup>
import { ref, computed, watch } from 'vue'
import { formatDuration } from '@/utils/formatUtils'

const props = defineProps({
  // 显示状态
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 标题
  title: {
    type: String,
    default: '选择时间',
  },
  // 选中的值（秒）
  value: {
    type: Number,
    default: 0,
  },
  // 最大小时数
  maxHours: {
    type: Number,
    default: 24,
  },
})

const emit = defineEmits(['update:modelValue', 'update:value', 'change'])

const popup = ref(null)
// 根据maxHours生成小时数组
const hoursArray = computed(() => {
  return Array.from({ length: props.maxHours + 1 }, (_, i) => i) // 0到maxHours（包含）
})
const minutesArray = Array.from({ length: 60 }, (_, i) => i) // 0-59分钟

// 当前选中的值
const currentValue = ref([0, 0])

// 监听value变化，更新currentValue
watch(
  () => props.value,
  newVal => {
    if (newVal !== undefined) {
      const hours = Math.floor(newVal / 3600)
      const minutes = Math.floor((newVal % 3600) / 60)
      // 确保小时数不超过最大值
      currentValue.value = [Math.min(hours, props.maxHours), minutes]
    }
  },
  { immediate: true }
)

// 监听maxHours变化，更新currentValue
watch(
  () => props.maxHours,
  newVal => {
    if (props.value) {
      const hours = Math.floor(props.value / 3600)
      const minutes = Math.floor((props.value % 3600) / 60)
      // 如果当前选中的小时数大于新的maxHours，则调整为maxHours
      if (hours > newVal) {
        currentValue.value = [newVal, minutes]
        // 同时更新value值
        const seconds = newVal * 3600 + minutes * 60
        emit('update:value', seconds)
      }
    }
  }
)

// 监听显示状态
watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      // 打开时，根据当前value设置选中值
      const hours = Math.floor(props.value / 3600)
      const minutes = Math.floor((props.value % 3600) / 60)
      // 确保小时数不超过最大值
      currentValue.value = [Math.min(hours, props.maxHours), minutes]
      popup.value.open('bottom')
    } else {
      popup.value.close()
    }
  }
)

// 处理选择器变化
const handlePickerChange = e => {
  currentValue.value = e.detail.value
  const hours = hoursArray.value[e.detail.value[0]]
  const minutes = minutesArray[e.detail.value[1]]
  const seconds = hours * 3600 + minutes * 60
  emit('change', seconds)
}

// 取消选择
const handleCancel = () => {
  // 重置为原来的值
  const hours = Math.floor(props.value / 3600)
  const minutes = Math.floor((props.value % 3600) / 60)
  // 确保小时数不超过最大值
  currentValue.value = [Math.min(hours, props.maxHours), minutes]
  popup.value.close()
  emit('update:modelValue', false)
}

// 确认选择
const handleConfirm = () => {
  const hours = hoursArray.value[currentValue.value[0]]
  const minutes = minutesArray[currentValue.value[1]]
  const seconds = hours * 3600 + minutes * 60
  emit('update:value', seconds)
  popup.value.close()
  emit('update:modelValue', false)
}

// 处理弹窗状态变化
const handlePopupChange = e => {
  // 当弹窗关闭时，同步更新 modelValue
  if (e.show === false) {
    emit('update:modelValue', false)
  }
}

// 暴露方法给父组件
defineExpose({
  formatTime: formatDuration,
})
</script>

<style lang="scss" scoped>
.picker-container {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.cancel-btn {
  font-size: 32rpx;
  color: #999;
}

.confirm-btn {
  font-size: 32rpx;
  color: #2979ff;
}

.picker-body {
  height: 500rpx;
}

.picker-view {
  width: 100%;
  height: 100%;
}

.picker-item {
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}
</style>
