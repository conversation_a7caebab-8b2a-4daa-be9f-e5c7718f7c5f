<template>
  <view class="example-container">
    <view class="example-section">
      <text class="section-title">CustomDateTimePicker 使用示例</text>

      <!-- 示例1：完整日期时间选择 -->
      <view class="example-item">
        <text class="example-label">完整日期时间选择：</text>
        <view class="input-wrapper" @tap="showPicker1 = true">
          <text class="input-text">{{ formattedDateTime1 || '请选择日期时间' }}</text>
        </view>
        <text class="format-text">格式：{{ format1 }}</text>
      </view>

      <!-- 示例2：只显示年月日时分 -->
      <view class="example-item">
        <text class="example-label">年月日时分选择：</text>
        <view class="input-wrapper" @tap="showPicker2 = true">
          <text class="input-text">{{ formattedDateTime2 || '请选择日期时间' }}</text>
        </view>
        <text class="format-text">格式：{{ format2 }}</text>
      </view>

      <!-- 示例3：只显示年月日 -->
      <view class="example-item">
        <text class="example-label">只选择日期：</text>
        <view class="input-wrapper" @tap="showPicker3 = true">
          <text class="input-text">{{ formattedDateTime3 || '请选择日期' }}</text>
        </view>
        <text class="format-text">格式：{{ format3 }}</text>
      </view>

      <!-- 示例4：自定义格式 -->
      <view class="example-item">
        <text class="example-label">自定义格式：</text>
        <view class="input-wrapper" @tap="showPicker4 = true">
          <text class="input-text">{{ formattedDateTime4 || '请选择日期时间' }}</text>
        </view>
        <text class="format-text">格式：{{ format4 }}</text>
      </view>
    </view>

    <!-- 日期时间选择器组件 -->
    <!-- 示例1：完整日期时间 -->
    <CustomDateTimePicker
      v-model="showPicker1"
      v-model:value="selectedDateTime1"
      :format="format1"
      title="选择完整日期时间"
      :show-time="true"
      :show-seconds="true"
      @change="handleDateTimeChange1"
    />

    <!-- 示例2：年月日时分 -->
    <CustomDateTimePicker
      v-model="showPicker2"
      v-model:value="selectedDateTime2"
      :format="format2"
      title="选择年月日时分"
      :show-time="true"
      :show-seconds="false"
      @change="handleDateTimeChange2"
    />

    <!-- 示例3：只选择日期 -->
    <CustomDateTimePicker
      v-model="showPicker3"
      v-model:value="selectedDateTime3"
      :format="format3"
      title="选择日期"
      :show-time="false"
      @change="handleDateTimeChange3"
    />

    <!-- 示例4：自定义格式 -->
    <CustomDateTimePicker
      v-model="showPicker4"
      v-model:value="selectedDateTime4"
      :format="format4"
      title="自定义格式选择"
      :show-time="true"
      :show-seconds="true"
      @change="handleDateTimeChange4"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import CustomDateTimePicker from './CustomDateTimePicker.vue'

// 控制选择器显示状态
const showPicker1 = ref(false)
const showPicker2 = ref(false)
const showPicker3 = ref(false)
const showPicker4 = ref(false)

// 选中的日期时间值
const selectedDateTime1 = ref(new Date())
const selectedDateTime2 = ref(new Date())
const selectedDateTime3 = ref(new Date())
const selectedDateTime4 = ref(new Date())

// 格式化格式
const format1 = 'yyyy-MM-dd HH:mm:ss'
const format2 = 'yyyy-MM-dd HH:mm'
const format3 = 'yyyy-MM-dd'
const format4 = 'yyyy/MM/dd HH:mm:ss'

// 格式化显示的日期时间
const formattedDateTime1 = computed(() => {
  return formatDate(selectedDateTime1.value, format1)
})

const formattedDateTime2 = computed(() => {
  return formatDate(selectedDateTime2.value, format2)
})

const formattedDateTime3 = computed(() => {
  return formatDate(selectedDateTime3.value, format3)
})

const formattedDateTime4 = computed(() => {
  return formatDate(selectedDateTime4.value, format4)
})

// 格式化日期函数
function formatDate(date, formatStr) {
  if (!date || isNaN(new Date(date).getTime())) return ''

  const dateObj = new Date(date)
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hour = String(dateObj.getHours()).padStart(2, '0')
  const minute = String(dateObj.getMinutes()).padStart(2, '0')
  const second = String(dateObj.getSeconds()).padStart(2, '0')

  return formatStr
    .replace(/yyyy/g, year)
    .replace(/MM/g, month)
    .replace(/dd/g, day)
    .replace(/HH/g, hour)
    .replace(/mm/g, minute)
    .replace(/ss/g, second)
}

// 处理日期时间变化事件（只在点击确定时触发）
function handleDateTimeChange1(date) {
  console.log('示例1 - 确认选择的日期时间：', date)
}

function handleDateTimeChange2(date) {
  console.log('示例2 - 确认选择的日期时间：', date)
}

function handleDateTimeChange3(date) {
  console.log('示例3 - 确认选择的日期：', date)
}

function handleDateTimeChange4(date) {
  console.log('示例4 - 确认选择的日期时间：', date)
}
</script>

<style lang="scss" scoped>
.example-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.example-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.example-item {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.example-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.input-wrapper {
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 10rpx;
}

.input-text {
  font-size: 30rpx;
  color: #333;
}

.format-text {
  font-size: 24rpx;
  color: #999;
}
</style>
