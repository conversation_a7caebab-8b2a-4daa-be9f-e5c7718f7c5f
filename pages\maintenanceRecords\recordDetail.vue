<template>
  <view class="record-detail-container">
    <!-- 标签页 -->
    <CustomTab :tabs="tabList" v-model="activeTabIndex" style-type="text" @change="handleTabChange" />

    <!-- 基本信息区域 -->
    <scroll-view scroll-y class="content-area" v-if="activeTabIndex == 0">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">
          <view class="title-icon"></view>
          <text>基本信息</text>
        </view>

        <view class="info-item">
          <text class="item-label">订单编号</text>
          <text class="item-value">{{ recordData.orderCode || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">订单状态</text>
          <text class="item-value">{{ recordData.stateName || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">紧急程度</text>
          <text class="item-value">{{ recordData.maintenOrderUrgencyName || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">订单类型</text>
          <text class="item-value">{{ recordData.typeName || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">维保期限</text>
          <text class="item-value">{{ formatDuration(recordData.maintenPeriod) || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">开始时间</text>
          <text class="item-value">{{ recordData.startTime || '--' }}</text>
        </view>

        <view class="info-item">
          <text class="item-label">超时时间</text>
          <text class="item-value">{{ recordData.timeOut || '--' }}</text>
        </view>
      </view>

      <!-- 处理结果区域 - 仅在已完成或已作废状态显示 -->
      <block v-if="isCompleted">
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon"></view>
            <text>处理结果</text>
          </view>
          <view class="result-content">
            <text>{{ recordData.processResult || '暂无处理结果' }}</text>
          </view>
        </view>

        <!-- 作废原因区域 - 仅在已作废状态显示 -->
        <view class="info-section" v-if="recordData.state == 2">
          <view class="section-title">
            <view class="title-icon"></view>
            <text>作废原因</text>
          </view>
          <view class="result-content">
            <text>{{ recordData.cancel || '暂无作废原因' }}</text>
          </view>
        </view>

        <!-- 备注信息区域 -->
        <view class="info-section">
          <view class="section-title">
            <view class="title-icon"></view>
            <text>备注信息</text>
            <!-- <view class="edit-btn" @tap="editRemark">编辑</view> -->
          </view>
          <view class="result-content">
            <text>{{ recordData.remark || '暂无备注信息' }}</text>
          </view>
        </view>
      </block>
    </scroll-view>

    <!-- 设备清单区域 -->
    <scroll-view scroll-y class="content-area" v-if="activeTabIndex == 1">
      <view class="device-grid">
        <view
          class="device-card"
          v-for="(item, index) in recordData.deviceList"
          :key="index"
          @tap="handleDeviceClick(item)"
        >
          <image class="device-image" src="/static/images/maintenance/device.png" mode="aspectFit"></image>
          <view class="device-info">
            <view class="device-code">{{ item.deviceName }}</view>
            <view class="device-serial">{{ item.categoryName }}</view>
          </view>
          <view
            class="device-status-tag"
            :class="{
              'status-filled': isDeviceProcessed(item),
              'status-unfilled': !isDeviceProcessed(item),
            }"
          >
            {{ getDeviceStatusText(item) }}
          </view>
        </view>
      </view>
      <EmptyData v-if="!recordData.deviceList || recordData.deviceList.length == 0" text="暂无设备数据" />
    </scroll-view>

    <view v-if="mode == 'edit'" class="submit-button-container">
      <button hover-class="button-hover" @tap="handleProcess">处理</button>
    </view>

    <!-- 基于 uni-popup-dialog 的输入弹窗组件 -->
    <UniInputDialog />
  </view>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CustomTab from '@/components/CustomTab/CustomTab.vue'
import EmptyData from '@/components/EmptyData/EmptyData.vue'
import UniInputDialog from '@/components/UniInputDialog/UniInputDialog.vue'
import { getMaintenanceRecordDetail, submitProcessResult } from '@/api/maintenance'
import { formatDuration } from '@/utils/formatUtils'

const { proxy } = getCurrentInstance()

// 标签页数据
const tabList = ['基本信息', '设备清单']
const activeTabIndex = ref(0)

const recordId = ref('') // 订单ID
const mode = ref('view') // 页面模式：view-查看 edit-编辑

// 处理标签页切换
function handleTabChange(e) {}

// 记录数据
const recordData = ref({
  id: '', // 订单ID
  orderCode: '', // 订单编号
  state: 0, // 订单状态（0:未完成 1:已完成 2:已作废 3:草稿箱）
  stateName: '', // 状态名称
  maintenOrderUrgency: '', // 紧急程度ID
  maintenOrderUrgencyName: '', // 紧急程度name
  type: '', // 订单类型
  typeName: '', // 类型名称
  maintenPeriod: 0, // 维保期限
  startTime: '', // 开始时间
  endTime: '', // 结束时间
  timeOut: '', // 超时时间
  processResult: '', // 处理结果
  cancel: '', // 作废原因
  remarks: '', // 备注
  deviceList: [], // 设备列表
})

// 是否可以提交（所有设备都已处理完成）
const canSubmit = computed(() => {
  // 判断是否还有未处理的设备
  let hasUnprocessedDevices = recordData.value.deviceList.some(item => !isDeviceProcessed(item))
  // 有设备且所有设备都已处理完成时才能提交
  return recordData.value.deviceList.length > 0 && !hasUnprocessedDevices
})

async function handleProcess() {
  if (!canSubmit.value) {
    return proxy.$modal.msg('当前还存在未处理的设备，请先全部处理完成后再提交！')
  }

  // 使用简化版本的 promptInput 让用户填写提交说明
  const submitRemark = await proxy.$modal.promptInput('提交确认', '请填写提交说明', '')

  // 用户确认后提交处理结果
  proxy.$modal.loading('提交中...')

  const submitData = {
    id: recordData.value.id,
    processResult: submitRemark,
  }

  submitProcessResult(submitData)
    .then(res => {
      proxy.$modal.msgSuccess('提交成功')

      setTimeout(() => {
        // 触发列表刷新
        uni.$emit('refreshMaintenanceList')
        proxy.$tab.navigateBack()
      }, 1000)
    })
    .catch(error => {
      proxy.$modal.msgError(error.message || '提交失败，请重试')
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}

async function getRecordDetail() {
  proxy.$modal.loading('加载中...')
  try {
    const res = await getMaintenanceRecordDetail({ id: recordId.value })
    recordData.value = res.data
  } catch (error) {
    proxy.$modal.msgError(error.message)
  } finally {
    proxy.$modal.closeLoading()
  }
}

function handleDeviceClick(item) {
  uni.setStorageSync('currentDeviceItem', item)
  proxy.$tab.navigateTo(
    `/pages/maintenanceRecords/processMaintenanceRecord?mode=${mode.value}&orderType=${recordData.value.type}`
  )
}

// 判断设备处理状态
function isDeviceProcessed(item) {
  // 当 !processResultType && processResultType != 0 时为未处理，反之为已处理
  return !(!item.processResultType && item.processResultType != 0)
}

// 获取设备处理状态文本
function getDeviceStatusText(item) {
  return isDeviceProcessed(item) ? '已处理' : '未处理'
}

// 计算属性：判断是否已完成或已作废
const isCompleted = computed(() => {
  return recordData.value.state == 1 || recordData.value.state == 2
})

onLoad(options => {
  recordId.value = options.recordId
  mode.value = options.mode ?? 'view'

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: mode.value == 'view' ? '查看维保记录详情' : '处理维保记录',
  })

  getRecordDetail()
})

onMounted(() => {
  uni.$on('refreshMaintenanceDetail', () => {
    getRecordDetail()
  })
})

onUnmounted(() => {
  uni.$off('refreshMaintenanceDetail')
})
</script>

<style lang="scss" scoped>
.record-detail-container {
  display: flex;
  flex-direction: column;
  height: $uni-height-area;
  background-color: #f5f5f5;
}

.page-header {
  position: relative;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.back-icon {
  position: absolute;
  left: 30rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
  background-color: transparent;
}

.info-section {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;

  .title-icon {
    width: 8rpx;
    height: 32rpx;
    background-color: #2979ff;
    margin-right: 20rpx;
    border-radius: 4rpx;
  }

  text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .edit-btn {
    position: absolute;
    right: 0;
    font-size: 28rpx;
    color: #2979ff;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.item-label {
  font-size: 28rpx;
  color: #666;
}

.item-value {
  font-size: 28rpx;
  color: #333;
}

.result-content {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 设备清单样式 */
.device-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  width: 100%;
}

.device-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 28rpx 20rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
}

.device-image {
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  object-fit: contain;
  flex-shrink: 0;
}

.device-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.device-code {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.device-serial {
  font-size: 24rpx;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.device-status-tag {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-top-right-radius: 16rpx;
  border-bottom-left-radius: 16rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-weight: 400;

  &.status-filled {
    background-image: url('/static/images/maintenance/grey-bg.png');
    color: #ffffff;
  }

  &.status-unfilled {
    background-image: url('/static/images/maintenance/blue-bg.png');
    color: #ffffff;
  }
}

.submit-button-container {
  width: 100%;
  padding: 0 30rpx 30rpx;

  button {
    background-color: #2979ff;
    color: #fff;
  }
}
</style>
