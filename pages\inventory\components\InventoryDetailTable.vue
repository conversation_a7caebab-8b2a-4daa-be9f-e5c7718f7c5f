<template>
  <view class="detail-table">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 表格内容 -->
    <view v-else class="table-container">
      <uni-table border stripe>
        <!-- 表头 -->
        <uni-tr class="table-header">
          <uni-th width="80" align="center">序号</uni-th>
          <uni-th width="160" align="center">设备编号</uni-th>
          <uni-th width="160" align="center">入库时间</uni-th>
          <uni-th width="120" align="center">备注</uni-th>
        </uni-tr>

        <!-- 表格数据 -->
        <uni-tr v-for="(item, index) in displayData" :key="index" class="table-row">
          <uni-td width="80" align="center">{{ index + 1 }}</uni-td>
          <uni-td width="160" align="center">{{ item.code }}</uni-td>
          <uni-td width="160" align="center">{{ item.createTime }}</uni-td>
          <uni-td width="120" align="center">{{ item.remark }}</uni-td>
        </uni-tr>

        <!-- 空数据提示 -->
        <uni-tr v-if="!loading && displayData.length === 0">
          <uni-td colspan="4" align="center" class="empty-data">
            <text>暂无数据</text>
          </uni-td>
        </uni-tr>
      </uni-table>
    </view>
  </view>
</template>

<script>
export default {
  name: 'InventoryDetailTable',
}
</script>

<script setup>
import { computed } from 'vue'

// 定义props
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 显示数据
const displayData = computed(() => {
  return props.data
})
</script>

<style lang="scss" scoped>
.detail-table {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-container {
  padding: 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.table-container {
  padding: 20rpx;
  height: 100%;
  overflow-y: auto;
}

.table-header {
  background-color: #f8f9fa;

  :deep(.uni-table-th) {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    padding: 24rpx 16rpx;
  }
}

.table-row {
  :deep(.uni-table-td) {
    font-size: 26rpx;
    color: #666;
    padding: 24rpx 16rpx;
  }
}

.empty-data {
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}

// 表格样式优化
:deep(.uni-table) {
  border-radius: 12rpx;
  overflow: hidden;

  .uni-table-tr {
    &:hover {
      background-color: #f8f9fa;
    }
  }

  .uni-table-th,
  .uni-table-td {
    border-color: #e9ecef;
  }
}
</style>
